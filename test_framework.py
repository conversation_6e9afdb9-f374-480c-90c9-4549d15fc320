#!/usr/bin/env python3
"""
Simple test script for IDSL LLM Framework
This script tests the basic functionality without requiring <PERSON><PERSON><PERSON>
"""

import os
import sys
import yaml
import argparse
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility"""
    print("🧪 Testing Python version...")

    import sys
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"

    print(f"🐍 Python version: {version_str}")

    if version.major != 3:
        print("❌ Python 3.x is required")
        return False

    if version.minor == 13:
        print("❌ Python 3.13 is NOT supported (incompatible with Unsloth, vLLM)")
        return False

    if version.minor in [9, 10, 11, 12]:
        print("✅ Python version is compatible")
        return True

    print(f"⚠️  Python 3.{version.minor} compatibility unknown")
    return True

def test_basic_imports():
    """Test if basic dependencies can be imported"""
    print("\n🧪 Testing basic imports...")

    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        print("❌ PyTorch not found. Install with: pip install torch")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError:
        print("❌ Transformers not found. Install with: pip install transformers")
        return False
    
    try:
        import datasets
        print(f"✅ Datasets: {datasets.__version__}")
    except ImportError:
        print("❌ Datasets not found. Install with: pip install datasets")
        return False
    
    return True

def test_config_loading():
    """Test loading configuration files"""
    print("\n🧪 Testing configuration loading...")
    
    config_files = [
        "unsloth_sft_config.yml",
        "axolotl_sft_config.yml", 
        "dynamic_batching_config.yml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = yaml.safe_load(f)
                print(f"✅ {config_file} loaded successfully")
            except Exception as e:
                print(f"❌ Error loading {config_file}: {e}")
                return False
        else:
            print(f"⚠️  {config_file} not found")
    
    return True

def test_dataset_wrappers():
    """Test dataset wrapper imports"""
    print("\n🧪 Testing dataset wrappers...")
    
    try:
        from dataset_wrappers.wrapper import sft_dataset, dpo_dataset, grpo_dataset
        print("✅ Base dataset wrappers imported successfully")
    except ImportError as e:
        print(f"❌ Error importing dataset wrappers: {e}")
        return False
    
    # Test specific dataset wrappers
    dataset_types = ["alpaca", "openhermes", "ultrafeedback"]
    
    for dataset_type in dataset_types:
        try:
            module = importlib.import_module(f'dataset_wrappers.{dataset_type}')
            dataset_class = getattr(module, f'{dataset_type}_dataset')
            print(f"✅ {dataset_type}_dataset imported successfully")
        except (ImportError, AttributeError) as e:
            print(f"❌ Error importing {dataset_type}_dataset: {e}")
    
    return True

def test_simple_dataset_loading():
    """Test simple dataset loading without full training"""
    print("\n🧪 Testing simple dataset loading...")

    try:
        # Test basic dataset loading without Unsloth dependencies
        from datasets import load_dataset

        print("Testing basic dataset loading...")
        # Load a small dataset for testing
        dataset = load_dataset("tatsu-lab/alpaca", split="train[:10]")

        print("✅ Basic dataset loading successful")
        print(f"Dataset size: {len(dataset)}")
        print(f"Dataset features: {list(dataset.features.keys())}")

        return True

    except Exception as e:
        print(f"❌ Error in dataset loading test: {e}")
        return False

def test_cli_script():
    """Test if CLI script exists and is executable"""
    print("\n🧪 Testing CLI script...")
    
    if os.path.exists("cli.sh"):
        if os.access("cli.sh", os.X_OK):
            print("✅ cli.sh exists and is executable")
        else:
            print("⚠️  cli.sh exists but is not executable. Run: chmod +x cli.sh")
        return True
    else:
        print("❌ cli.sh not found")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing required dependencies...")
    
    dependencies = [
        "torch",
        "transformers",
        "datasets", 
        "accelerate",
        "peft",
        "trl",
        "bitsandbytes",
        "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git",
        "jupyter",
        "notebook",
        "pyyaml",
        "tqdm"
    ]
    
    print("Run the following commands to install dependencies:")
    print("pip install torch transformers datasets accelerate peft trl bitsandbytes pyyaml tqdm jupyter notebook")
    print("pip install 'unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git'")

def main():
    parser = argparse.ArgumentParser(description="Test IDSL LLM Framework")
    parser.add_argument("--install-deps", action="store_true", help="Show dependency installation commands")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    
    args = parser.parse_args()
    
    print("🚀 IDSL LLM Framework Test Suite")
    print("=" * 50)
    
    if args.install_deps:
        install_dependencies()
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 0

    # Python version test
    total_tests += 1
    if test_python_version():
        tests_passed += 1

    # Basic import test
    total_tests += 1
    if test_basic_imports():
        tests_passed += 1
    
    # Config loading test
    total_tests += 1
    if test_config_loading():
        tests_passed += 1
    
    # Dataset wrapper test
    total_tests += 1
    if test_dataset_wrappers():
        tests_passed += 1
    
    # CLI script test
    total_tests += 1
    if test_cli_script():
        tests_passed += 1
    
    # Dataset loading test (only if not quick mode)
    if not args.quick:
        total_tests += 1
        if test_simple_dataset_loading():
            tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Framework is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        if tests_passed < 2:
            print("\n💡 Try running: python test_framework.py --install-deps")

if __name__ == "__main__":
    main()
