{"zero_optimization": {"stage": 2, "offload_optimizer": {"device": "cpu"}, "contiguous_gradients": true, "overlap_comm": true}, "bf16": {"enabled": "auto"}, "fp16": {"enabled": "auto", "auto_cast": false, "loss_scale": 0, "initial_scale_power": 32, "loss_scale_window": 1000, "hysteresis": 2, "min_loss_scale": 1}, "gradient_accumulation_steps": "auto", "gradient_clipping": "auto", "train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "wall_clock_breakdown": false}