#%%
import os, sys
import tqdm
from os import listdir
from os.path import isfile, isdir, join
from dotenv import load_dotenv
os.environ['LD_LIBRARY_PATH'] = '/usr/lib/x86_64-linux-gnu'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
load_dotenv()

import math, random
import numpy as np
import pandas as pd
import cv2, imutils
import json, collections
import pickle as pkl
import multiprocessing
import argparse, pprint
import yaml, configparser
import shutil, gc, copy
import importlib, warnings
import time, requests

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F

from PIL import Image
from datetime import datetime
from argparse import Namespace
from types import ModuleType
from typing import Iterable, Callable, Dict, Literal
from torcheval.metrics.functional import multiclass_f1_score, multiclass_accuracy
from torchinfo import summary

from torch.nn.utils.rnn import pad_sequence
from transformers import get_linear_schedule_with_warmup, pipeline, TextStreamer, TrainerCallback
from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig
from transformers import BitsAndBytesConfig, TrainingArguments, logging
from transformers import DataCollatorForSeq2Seq
from datasets import load_dataset, DatasetDict, Dataset, load_from_disk, concatenate_datasets
from tokenizers import tokenizers

import huggingface_hub
import accelerate, deepspeed
import bitsandbytes as bnb

from trl import DPOConfig, GRPOConfig, DataCollatorForCompletionOnlyLM
from peft import LoftQConfig, LoraConfig, PeftModel, TaskType, get_peft_model, prepare_model_for_kbit_training
from peft.utils import _get_submodules
from accelerate.utils import load_and_quantize_model
from accelerate.state import PartialState, AcceleratorState
from trainer.custom_sft_trainer import SFTTrainer
from trainer.custom_dpo_trainer import DPOTrainer
from trainer.custom_grpo_trainer import GRPOTrainer
from trainer.rope_scaling import ROPE_CONFIG
from unsloth.chat_templates import get_chat_template

if sys.argv[1]=='DEFAULT':
    sys.argv = [
        'script.py',
        '--training_config', './dynamic_batching_config.yml',
        '--cache_dir', '/data/server_cache',
        '--adapter_type', 'LoRA',
        '--quant_bits', '4',
        '--gpu_parallel', '1',
        '--lora_rank', '16',
        '--lora_dropout', '0',
        '--save_gguf', 'q4_k_m',
        '--max_seq_len', '16384',
        '--BUCKET_MIN_SIZE', '1024',
        '--BUCKET_MAX_SIZE', '16384',
        '--save_merged',
        f'{sys.argv[2]}',
    ]

pp = pprint.PrettyPrinter(indent=2)
parser = argparse.ArgumentParser()
parser.add_argument('--training_config', type=str, required=True)
parser.add_argument('--cache_dir', type=str, default='../server_cache')
parser.add_argument('--model_name', type=str, default='meta-llama/Llama-3.2-3B-Instruct')
parser.add_argument('--accelerator_config', type=str, default='./accelerator_config.yml')
parser.add_argument('--reference_model_path', type=str, required=False)
parser.add_argument('--lora_rank', type=int, required=False, default=16)
parser.add_argument('--lora_alpha', type=int, required=False, default=0)
parser.add_argument('--lora_dropout', type=float, required=False, default=0.1)
parser.add_argument('--gpu_parallel', type=int, required=False, default=1)
parser.add_argument('--max_seq_len', type=int, required=False, default=32768)
parser.add_argument('--quant_bits', type=int, required=False, default=4, choices=[4, 8])
parser.add_argument('--adapter_type', type=str, required=False, default='LoRA', choices=['LoRA', 'QLoRA', 'DoRA', 'RSLoRA', 'LoftQ'])
parser.add_argument('--save_gguf', type=str, required=False, default='')
parser.add_argument('--sft', action='store_true', required=False)
parser.add_argument('--dpo', action='store_true', required=False)
parser.add_argument('--rl', action='store_true', required=False)
parser.add_argument('--save_merged', action='store_true', required=False)
parser.add_argument('--BUCKET_MIN_SIZE', type=int, default=1024, help='Min token size for dynamic batching.')
parser.add_argument('--BUCKET_MAX_SIZE', type=int, default=32768, help='Max token size for dynamic batching.')
args = parser.parse_args()
with open(args.training_config, 'r') as file:
    training_config_dict = yaml.safe_load(file)
with open(args.accelerator_config, 'r') as file:
    accelerator_config_dict = yaml.safe_load(file)

pp.pprint(training_config_dict)
try:
    assert np.sum(np.array([args.sft, args.dpo, args.rl]))==1
    if args.save_gguf is not None and not args.save_merged:
        print("Asserting --save_merged to enable --save_gguf flag")
        args.save_merged = True
except Exception as e:
    print("Please select only one of ['--sft', '--dpo', '--rl].")
    print("Using --sft for execution:")
    args.sft = True
    args.dpo = False
    args.rl = False

# warnings.filterwarnings("ignore")
# torch.set_warn_always(False)
token = training_config_dict['hub_config']['hf_token']
huggingface_hub.login(token=token if token is not None else os.environ['HF_TOKEN'], add_to_git_credential=False)
num_processes = multiprocessing.cpu_count()//2
# deepspeed_plugin = accelerate.DeepSpeedPlugin(hf_ds_config="/home/<USER>/IDSL_LLM_Docker/deepspeed_configs/zero3_bf16_cpuoffload_all.json")
# accelerator = accelerate.Accelerator(deepspeed_plugin=deepspeed_plugin)
accelerator = accelerate.Accelerator()
state = PartialState()
astate = AcceleratorState()
gpu_map = {'': accelerator.local_process_index}
print(state, gpu_map)
if astate.is_local_main_process:
    print(args)

#%%
@state.on_local_main_process
def download_model(model_name, model_path, trust_remote_code=True):
    if not isdir(model_path):
        huggingface_hub.snapshot_download(repo_id=model_name, local_dir=model_path)
        model_config = AutoConfig.from_pretrained(
            model_name,
            token = token if token is not None else os.environ['HF_TOKEN'],
            trust_remote_code = trust_remote_code,
        )
        pp.pprint(model_config)
        
@state.on_local_main_process
def test_prompt(model, tokenizer):
    streamer = TextStreamer(tokenizer)
    pipe = pipeline(model=model, tokenizer=tokenizer, streamer=streamer,
                    config=model_config, task='text-generation', num_return_sequences=1)
    generation_settings = {
        'do_sample': True,
        'temperature' : 0.7,
        'top_p' : 0.95,
        'top_k': 40,
        'repetition_penalty' : 1.1,
        'max_new_tokens' : 1024,
    }
    message = [
        {
            "role": "system",
            "content": "You are a helpful and friendly chatbot. Please provide an answer to the following user query.",
        },
        {
            "role": "user",
            "content": "Tell me a funny joke about Large Language Models."
        },
    ]
    prompt = tokenizer.apply_chat_template(message, tokenize=False, add_generation_prompt=True)
    pipe(prompt, **generation_settings)

def formatting_func(examples, tokenizer, return_type: Literal["standard", "conversational"] = "standard"):
    b_sz = len(examples["prompt"])
    chosen = []
    rejected = []
    
    for b_id in range(b_sz):
        if return_type=="standard":
            chosen_prompt = tokenizer.apply_chat_template(examples["prompt"][b_id] + examples["chosen"][b_id][-1:], tokenize=False)
            rejected_prompt = tokenizer.apply_chat_template(examples["prompt"][b_id] + examples["rejected"][b_id][-1:], tokenize=False)
        if return_type=="conversational":
            chosen_prompt = examples["prompt"][b_id] + examples["chosen"][b_id][-1:]
            rejected_prompt = examples["prompt"][b_id] + examples["rejected"][b_id][-1:]
        chosen.append(chosen_prompt)
        rejected.append(rejected_prompt)
    return {"chosen" : chosen, "rejected": rejected}

def get_dataset_buckets(dataset, args, len_factor=2, base_prompt_size=1024, ft_type='sft'):
    # print(dataset.iterable_dataset_traineval)
    iterable_dataset = {}
    batch_token_buckets_valid = []
    batch_token_buckets = [(f+1)*(base_prompt_size*len_factor) for f in range(0, args.max_seq_len//(base_prompt_size*len_factor))]
    
    for i in range(len(batch_token_buckets)):
        bucket_threshold = batch_token_buckets[i]
        include_idx = []
        for idx, data_row in enumerate(dataset.iterable_dataset_traineval['train']):
            if ft_type=='sft':
                text = data_row['text']
                tokenized_text = dataset.tokenizer(text, add_special_tokens=False)
            elif ft_type=='dpo':
                prompt = data_row['prompt']
                text = dataset.tokenizer.apply_chat_template(prompt, tokenize=False)
                tokenized_text = dataset.tokenizer(text, add_special_tokens=False)
            elif ft_type=='rl':
                prompt = data_row['prompt']
                text = dataset.tokenizer.apply_chat_template(prompt, tokenize=False)
                tokenized_text = dataset.tokenizer(text, add_special_tokens=False)
            tokenized_text_len = len(tokenized_text.input_ids)
            if tokenized_text_len<bucket_threshold and tokenized_text_len>=(bucket_threshold-(base_prompt_size*len_factor)):
                include_idx.append(idx)
        if len(include_idx)>0:
            if astate.is_local_main_process:
                print(f'Bucketing sequences between {bucket_threshold-(base_prompt_size*len_factor)}-{bucket_threshold}')
            iterable_dataset[str(bucket_threshold)] = dataset.iterable_dataset_traineval['train'].select(include_idx)
            batch_token_buckets_valid.append(bucket_threshold)
    if len(iterable_dataset.keys())>0:
        iterable_dataset = DatasetDict(iterable_dataset)
    return iterable_dataset, batch_token_buckets_valid
    
def get_smart_batch_sizes(
    batch_token_buckets, 
    downsample_factor = 4, 
    base_batch_size = 16, 
    min_bucket_size = 2048, 
    max_bucket_size = 16384,
    verbose=False,
):
    batch_token_buckets.sort()
    smart_batches = []
    for i in range(len(batch_token_buckets)):
        if batch_token_buckets[i]>max_bucket_size:
            break
        
        if batch_token_buckets[i]<=min_bucket_size:
            smart_batches.append(base_batch_size)
            if verbose and astate.is_local_main_process:
                print(batch_token_buckets[i], min_bucket_size)
        else:
            factor = downsample_factor**(int(batch_token_buckets[i]/min_bucket_size)-1)
            smart_batches.append(int(base_batch_size/factor))
            if verbose and astate.is_local_main_process:
                print(factor, batch_token_buckets[i], min_bucket_size)
    return smart_batches

#%%
if args.quant_bits==4:
    bnb_config_dict = {
        'bnb_load_in_4bit': True,
        'bnb_4bit_use_double_quant': True,
        'bnb_4bit_quant_type': 'nf4',
        'bnb_4bit_compute_dtype': torch.bfloat16,
    }
    quantization_config = BitsAndBytesConfig(load_in_4bit = bnb_config_dict['bnb_load_in_4bit'], 
                                            bnb_4bit_use_double_quant = bnb_config_dict['bnb_4bit_use_double_quant'],
                                            bnb_4bit_quant_type = bnb_config_dict['bnb_4bit_quant_type'],
                                            bnb_4bit_compute_dtype = bnb_config_dict['bnb_4bit_compute_dtype'],
                                            )
if args.quant_bits==8:
    quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    
#%%
trust_remote_code = training_config_dict['hub_config']['trust_remote_code']
dataset_args = training_config_dict['dataset_arguments']
if not isdir(join(args.cache_dir, args.model_name)):
    model_path = join(args.cache_dir, args.model_name.replace('/', '--'))
    download_model(args.model_name, model_path, trust_remote_code)
else:
    model_path = join(args.cache_dir, args.model_name)
    
#%%
if args.sft:
    dataset_dict = {}
    if dataset_args['dataset_split'] is not None:
        dataset_dict['split_name'] = dataset_args['dataset_split']
    if dataset_args['dataset_subset'] is not None:
        dataset_dict['subset_name'] = dataset_args['dataset_subset']
    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args["dataset_type"]}'), f'{dataset_args["dataset_type"]}_dataset')
    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], 
                                cache_dir=dataset_args['cache_dir'], 
                                test_size=dataset_args['test_split'], 
                                **dataset_dict,
                                )
    tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(model_path), chat_template=dataset_args['chat_template'])
    dataset_path = join(dataset_args['cache_dir'], 'processed_dynamic_batching_data')
    
    if astate.is_local_main_process:
        print('Dataset Loaded.')
        print('Performing SFT:')
        print(dataset_obj.iterable_dataset_traineval['train'])
        if isdir(dataset_path):
            os.system('rm -r "{}"'.format(dataset_path))
        dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)
    
    train_dataset, batch_token_buckets_valid = get_dataset_buckets(dataset_obj, args, 
                                                                len_factor=args.BUCKET_MIN_SIZE//1024, 
                                                                base_prompt_size=1024, ft_type='sft')
    smart_batches = get_smart_batch_sizes(
        batch_token_buckets_valid, 
        base_batch_size = training_config_dict['training_arguments']['per_device_train_batch_size'],
        min_bucket_size = args.BUCKET_MIN_SIZE,
        max_bucket_size = args.BUCKET_MAX_SIZE,
    )
else:
    if astate.is_local_main_process:
        print('SFT is not selected.')
    else:
        pass
    
if args.dpo:
    dataset_dict = {}
    if dataset_args['dataset_split'] is not None:
        dataset_dict['split_name'] = dataset_args['dataset_split']
    if dataset_args['dataset_subset'] is not None:
        dataset_dict['subset_name'] = dataset_args['dataset_subset']
    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args["dataset_type"]}'), f'{dataset_args["dataset_type"]}_dataset')
    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], 
                                cache_dir=dataset_args['cache_dir'], 
                                test_size=dataset_args['test_split'], 
                                **dataset_dict,
                                )
    dataset_obj.tokenizer = get_chat_template(AutoTokenizer.from_pretrained(model_path), chat_template = dataset_args['chat_template'])
    tokenizer = dataset_obj.tokenizer
    dataset_obj.chat_headers = {
        "system_header": dataset_args["system_header"], 
        "user_header": dataset_args["user_header"], 
        "assistant_header": dataset_args["assistant_header"]
    }
    custom_dpo_chat_template = "{% for message in messages %}{% if message['role'] == 'user' %}{{ message['content'] }}\
        {% elif message['role'] == 'assistant' %}{{ message['content'] }}{% endif %}{% endfor %}"
    dataset_obj.iterable_dataset_traineval = dataset_obj.iterable_dataset_traineval.map(formatting_func, batched = True, 
                                                                                        fn_kwargs = {
                                                                                            'tokenizer': tokenizer,
                                                                                            'return_type': 'conversational',
                                                                                        })
    for split_key in dataset_obj.iterable_dataset_traineval.keys():
        dataset_obj.iterable_dataset_traineval[split_key] = dataset_obj.iterable_dataset_traineval[split_key].remove_columns([
            f for f in dataset_obj.iterable_dataset_traineval[split_key].column_names if f not in ["chosen", "rejected"]
        ])
    dataset_path = join(dataset_args['cache_dir'], 'processed_dynamic_batching_data')
    
    if astate.is_local_main_process:
        print('Dataset Loaded.')
        print('Performing DPO:')
        print(dataset_obj.iterable_dataset_traineval['train'])
        if isdir(dataset_path):
            os.system('rm -r "{}"'.format(dataset_path))
        dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)
        
    train_dataset, batch_token_buckets_valid = get_dataset_buckets(dataset_obj, args, 
                                                                len_factor=args.BUCKET_MIN_SIZE//1024, 
                                                                base_prompt_size=1024, ft_type='dpo')
    smart_batches = get_smart_batch_sizes(
        batch_token_buckets_valid, 
        base_batch_size = training_config_dict['training_arguments']['per_device_train_batch_size'],
        min_bucket_size = args.BUCKET_MIN_SIZE,
        max_bucket_size = args.BUCKET_MAX_SIZE,
    )
else:
    if astate.is_local_main_process:
        print('DPO is not selected.')
    else:
        pass
    
if args.rl:
    dataset_dict = {}
    if dataset_args['dataset_split'] is not None:
        dataset_dict['split_name'] = dataset_args['dataset_split']
    if dataset_args['dataset_subset'] is not None:
        dataset_dict['subset_name'] = dataset_args['dataset_subset']
    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args["dataset_type"]}'), f'{dataset_args["dataset_type"]}_dataset')
    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], 
                                 cache_dir=dataset_args['cache_dir'], 
                                 test_size=dataset_args['test_split'], 
                                 **dataset_dict,
                                )
    tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(model_path), chat_template=dataset_args['chat_template'], 
                                            ft_type='rl',
                                            chat_headers={
                                                "system_header":dataset_args['system_header'], 
                                                "user_header":dataset_args['user_header'], 
                                                "assistant_header":dataset_args['assistant_header']
                                            })
    reward_fn_list = dataset_obj.format_rewards(training_config_dict['trl_arguments']['grpo_reward_funcs'])
    dataset_path = join(dataset_args['cache_dir'], 'processed_dynamic_batching_data')
    
    if astate.is_local_main_process:
        print('Dataset Loaded.')
        print('Performing RL:')
        print('Rewards:', [f.__name__  for f in reward_fn_list])
        print(dataset_obj.iterable_dataset_traineval['train'])
        if isdir(dataset_path):
            os.system('rm -r "{}"'.format(dataset_path))
        dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)
        
    train_dataset, batch_token_buckets_valid = get_dataset_buckets(dataset_obj, args, 
                                                                len_factor=args.BUCKET_MIN_SIZE//1024, 
                                                                base_prompt_size=1024, ft_type='rl')
    smart_batches = get_smart_batch_sizes(
        batch_token_buckets_valid, 
        base_batch_size = training_config_dict['training_arguments']['per_device_train_batch_size'],
        min_bucket_size = args.BUCKET_MIN_SIZE,
        max_bucket_size = args.BUCKET_MAX_SIZE,
    )
else:
    if astate.is_local_main_process:
        print('RL is not selected.')
    else:
        pass

state.wait_for_everyone()
accelerator.free_memory()

#%%
model_config = AutoConfig.from_pretrained(model_path).__dict__
if args.sft or args.dpo:
    model_kwargs = {'device_map': gpu_map}
else:
    model_kwargs = {'device_map': gpu_map}
if 'quantization_config' not in model_config.keys():
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        cache_dir=args.cache_dir, 
        trust_remote_code=trust_remote_code, revision='main',
        attn_implementation="flash_attention_2",
        torch_dtype=torch.bfloat16,
        quantization_config = quantization_config if args.adapter_type not in ['LoftQ', 'LoRA'] else None,
        **model_kwargs
    )
else:
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        cache_dir=args.cache_dir, 
        trust_remote_code=trust_remote_code, revision='main',
        attn_implementation="flash_attention_2",
        torch_dtype=torch.bfloat16,
        **model_kwargs
    )
    assert model_config['quantization_config']['quant_method'] == 'bitsandbytes'
    if not model_config['quantization_config'][f'load_in_{args.quant_bits}bit']:
        model.dequantize()
        if args.adapter_type not in ['LoftQ', 'LoRA']:
            model = load_and_quantize_model(model, bnb_quantization_config=quantization_config)
org_tokenizer = AutoTokenizer.from_pretrained(model_path)
if args.sft or args.dpo:
    test_prompt(model, org_tokenizer)
embeddings_len = len(tokenizer)
if(hasattr(model, "get_input_embeddings") and model.get_input_embeddings().num_embeddings < embeddings_len):
    model.resize_token_embeddings(embeddings_len)
    if astate.is_local_main_process:
        print('Model embedding lengths resized due to added tokens.')
state.wait_for_everyone()

#%%
target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                    "gate_proj", "up_proj", "down_proj",]
if args.adapter_type not in ['LoRA']:
    target_modules = 'all-linear'
    # target_modules = []
    # for name, module in model.named_modules():
    #     if isinstance(module, nn.Linear) and name!='lm_head':
    #         target_modules.append(name)
    #         print(name)
if args.adapter_type=='LoftQ':
    peft_kwargs = {
        'init_lora_weights': 'loftq' if args.adapter_type=='LoftQ' else None,
        'loftq_config': None if args.adapter_type!='LoftQ' else LoftQConfig(loftq_bits=args.quant_bits, loftq_iter=5),
    }
else:
    peft_kwargs = {}
peft_config = LoraConfig(
    # modules_to_save=["embed_tokens", "lm_head",],
    lora_alpha=args.lora_alpha,
    lora_dropout=args.lora_dropout,
    r=args.lora_rank,
    task_type="CAUSAL_LM",
    target_modules=target_modules,
    fan_in_fan_out=training_config_dict['training_arguments']['fan_in_fan_out'],
    use_rslora = True if args.adapter_type=='RSLoRA' else None,
    use_dora = True if args.adapter_type=='DoRA' else None,
    **peft_kwargs,
)
if tokenizer.pad_token_id is None:
    tokenizer.pad_token_id = tokenizer.eos_token_id

if args.sft or args.dpo:
    model.enable_input_require_grads()
    model.gradient_checkpointing_enable(gradient_checkpointing_kwargs={"use_reentrant": False})
    model = prepare_model_for_kbit_training(model, use_gradient_checkpointing=True)
    model = get_peft_model(model, peft_config)
else:
    model = get_peft_model(model, peft_config)

trainable, total = model.get_nb_trainable_parameters()
if astate.is_local_main_process:
    print(f"Trainable: {trainable} | total: {total} | Percentage: {trainable/total*100:.4f}%")
    print('\n'.join("%s: %s" % item for item in vars(peft_config).items()))
    print('Dynamic batching sizes:', smart_batches)
    model.print_trainable_parameters()
state.wait_for_everyone()

#%%
if args.sft:
    collator = DataCollatorForCompletionOnlyLM(
        instruction_template=dataset_args['user_header'], 
        response_template=dataset_args['assistant_header'], 
        tokenizer=tokenizer, mlm=False)
    optimizer = None
    total_epochs = training_config_dict['training_arguments']['num_train_epochs']
    initial_lr = training_config_dict['training_arguments']['learning_rate']
    end_lr = 0.
    lr_buckets = np.linspace(initial_lr, end_lr, total_epochs+1)

    for epoch_num in range(total_epochs):
        grad_accumulation_steps = training_config_dict['training_arguments']['gradient_accumulation_steps']
        scheduler_max_steps_factor = lr_buckets[epoch_num]/(lr_buckets[epoch_num]-lr_buckets[epoch_num+1])
        
        for b_id in range(len(smart_batches)):
            if batch_token_buckets_valid[b_id]<(args.BUCKET_MIN_SIZE):
                continue
            elif batch_token_buckets_valid[b_id]==(args.BUCKET_MIN_SIZE):
                concatenate_datasets_list = [str(f) for f in batch_token_buckets_valid if f<=(args.BUCKET_MIN_SIZE)]
                if len(concatenate_datasets_list)>1:
                    train_dataset['train'] = concatenate_datasets([train_dataset[k] for k in concatenate_datasets_list])
                else:
                    train_dataset['train'] = train_dataset[f'{args.BUCKET_MIN_SIZE}']
            else:
                train_dataset["train"] = train_dataset[str(batch_token_buckets_valid[b_id])]
                
            if 'test' in dataset_obj.iterable_dataset_traineval.keys():
                train_dataset["test"] = dataset_obj.iterable_dataset_traineval['test']
            else:
                train_dataset["test"] = None
            
            if smart_batches[b_id]:
                b_sz = smart_batches[b_id]
            else:
                b_sz = 1
                grad_accumulation_steps = max(1, grad_accumulation_steps//2)
            
            if astate.is_local_main_process:
                print(f'Bucket:{batch_token_buckets_valid[b_id]} | batch-size:{b_sz} | grad accumalation:{grad_accumulation_steps}\
                    \nprompt length: {batch_token_buckets_valid[b_id]} | prompt + chosen length: {batch_token_buckets_valid[max(0, b_id-1)]}')
                print(f'Initial LR: {lr_buckets[epoch_num]} | End LR: {lr_buckets[epoch_num+1]} | Factor: {scheduler_max_steps_factor}')
                pp.pprint(train_dataset)
            
            training_arguments = TrainingArguments(
                output_dir = training_config_dict['training_arguments']['output_dir'],
                num_train_epochs = 1,
                evaluation_strategy = "steps" if train_dataset['test'] is not None else "no",
                eval_steps = training_config_dict['training_arguments']['eval_steps'] if train_dataset['test'] is not None else 0,
                per_device_train_batch_size = b_sz,
                per_device_eval_batch_size = max(1, b_sz//2) if train_dataset["test"] is not None else 1,
                gradient_accumulation_steps = grad_accumulation_steps,
                optim = training_config_dict['training_arguments']['optim'],
                warmup_ratio = training_config_dict['training_arguments']['warmup_ratio'] if (epoch_num==0 and b_id==0) else 0,
                lr_scheduler_type = "linear" if b_id==0 else "constant",
                learning_rate = lr_buckets[epoch_num] if b_id==0 else lr_buckets[epoch_num+1],
                weight_decay = training_config_dict['training_arguments']['weight_decay'],
                max_grad_norm = training_config_dict['training_arguments']['max_grad_norm'],
                fp16 = False,
                bf16 = True,
                packing = False,
                group_by_length = True,
                # gradient_checkpointing = True,
                # gradient_checkpointing_kwargs = {'use_reentrant':False},
                save_steps = training_config_dict['training_arguments']['save_steps'],
                save_total_limit = training_config_dict['training_arguments']['save_total_limit'],
                seed = training_config_dict['training_arguments']['seed'],
                logging_steps = training_config_dict['training_arguments']['logging_steps'],
                report_to = training_config_dict['training_arguments']['report_to'],
                logging_dir = join(args.cache_dir, 'logging', datetime.now().strftime("%Y%m%d_%H%M%S")),
            )
            
            trainer = SFTTrainer(
                model = model,
                train_dataset = train_dataset['train'],
                eval_dataset = train_dataset['test'],
                dataset_text_field = training_config_dict['dataset_arguments']['field_texts'],
                peft_config = peft_config,
                args = training_arguments,
                tokenizer = tokenizer,
                max_seq_length = batch_token_buckets_valid[b_id], 
                data_collator = collator,
                dataset_num_proc = num_processes,
                optimizers = (optimizer, None),
                scheduler_max_steps_factor = scheduler_max_steps_factor,
            )
            trainer.train()
            state.wait_for_everyone()
            optimizer = trainer.optimizer
            save_model_path = model_path+f'_{args.adapter_type.lower()}'
            trainer.model.save_pretrained(save_model_path)
            if astate.is_local_main_process:
                tokenizer.save_pretrained(save_model_path)
            if (epoch_num+1) == total_epochs and batch_token_buckets_valid[b_id]==(args.BUCKET_MIN_SIZE):
                print(trainer.evaluate())
                break
            torch.cuda.empty_cache()
state.wait_for_everyone()

#%%
if args.dpo:
    optimizer = None
    total_epochs = training_config_dict['training_arguments']['num_train_epochs']
    initial_lr = training_config_dict['training_arguments']['learning_rate']
    end_lr = 0.
    lr_buckets = np.linspace(initial_lr, end_lr, total_epochs+1)

    for epoch_num in range(total_epochs):
        grad_accumulation_steps = training_config_dict['training_arguments']['gradient_accumulation_steps']
        scheduler_max_steps_factor = lr_buckets[epoch_num]/(lr_buckets[epoch_num]-lr_buckets[epoch_num+1])
        
        for b_id in range(len(smart_batches)):
            if batch_token_buckets_valid[b_id]<(args.BUCKET_MIN_SIZE):
                continue
            elif batch_token_buckets_valid[b_id]==(args.BUCKET_MIN_SIZE):
                concatenate_datasets_list = [str(f) for f in batch_token_buckets_valid if f<=(args.BUCKET_MIN_SIZE)]
                if len(concatenate_datasets_list)>1:
                    train_dataset['train'] = concatenate_datasets([train_dataset[k] for k in concatenate_datasets_list])
                else:
                    train_dataset['train'] = train_dataset[f'{args.BUCKET_MIN_SIZE}']
            else:
                train_dataset["train"] = train_dataset[str(batch_token_buckets_valid[b_id])]
                
            if 'test' in dataset_obj.iterable_dataset_traineval.keys():
                train_dataset["test"] = dataset_obj.iterable_dataset_traineval['test']
            else:
                train_dataset["test"] = None
            
            if smart_batches[b_id]:
                b_sz = smart_batches[b_id]
            else:
                b_sz = 1
                grad_accumulation_steps = max(1, grad_accumulation_steps//2)
            
            if astate.is_local_main_process:
                print(f'Bucket:{batch_token_buckets_valid[b_id]} | batch-size:{b_sz} | grad accumalation:{grad_accumulation_steps}\
                    \nprompt length: {batch_token_buckets_valid[b_id]} | prompt + chosen length: {batch_token_buckets_valid[max(0, b_id-1)]}')
                print(f'Initial LR: {lr_buckets[epoch_num]} | End LR: {lr_buckets[epoch_num+1]} | Factor: {scheduler_max_steps_factor}')
                pp.pprint(train_dataset)
    
            training_arguments = DPOConfig(
                output_dir = training_config_dict['training_arguments']['output_dir'],
                num_train_epochs = 1,
                evaluation_strategy = "steps" if train_dataset['test'] is not None else "no",
                eval_steps = training_config_dict['training_arguments']['eval_steps'] if train_dataset['test'] is not None else 0,
                per_device_train_batch_size = b_sz,
                per_device_eval_batch_size = max(1, b_sz//2) if train_dataset["test"] is not None else 1,
                gradient_accumulation_steps = grad_accumulation_steps,
                optim = training_config_dict['training_arguments']['optim'],
                warmup_ratio = training_config_dict['training_arguments']['warmup_ratio'] if (epoch_num==0 and b_id==0) else 0,
                lr_scheduler_type = "linear" if b_id==0 else "constant",
                learning_rate = lr_buckets[epoch_num] if b_id==0 else lr_buckets[epoch_num+1],
                weight_decay = training_config_dict['training_arguments']['weight_decay'],
                max_grad_norm = training_config_dict['training_arguments']['max_grad_norm'],
                fp16 = False,
                bf16 = True,
                # gradient_checkpointing = True,
                # gradient_checkpointing_kwargs = {'use_reentrant':False},
                save_steps = training_config_dict['training_arguments']['save_steps'],
                save_total_limit = training_config_dict['training_arguments']['save_total_limit'],
                seed = training_config_dict['training_arguments']['seed'],
                logging_steps = training_config_dict['training_arguments']['logging_steps'],
                report_to = training_config_dict['training_arguments']['report_to'],
                logging_dir = join(args.cache_dir, 'logging', datetime.now().strftime("%Y%m%d_%H%M%S")),
            )
            
            trainer = DPOTrainer(
                model = model,
                ref_model = None,
                args = training_arguments,
                # use_weighting = training_config_dict['trl_arguments']['dpo_use_weighting'] if training_config_dict['trl_arguments']['dpo_beta'] is not None else False,
                # rpo_alpha = training_config_dict['trl_arguments']['rpo_alpha'] if training_config_dict['trl_arguments']['dpo_beta'] is not None else None,
                beta = training_config_dict['trl_arguments']['dpo_beta'] if training_config_dict['trl_arguments']['dpo_beta'] is not None else 0.1,
                loss_type = training_config_dict['trl_arguments']['dpo_loss'] if training_config_dict['trl_arguments']['dpo_loss'] is not None else 0.1,
                train_dataset = train_dataset["train"],
                eval_dataset = train_dataset["test"],
                tokenizer = tokenizer,
                optimizers = (optimizer, None),
                max_length = batch_token_buckets_valid[b_id],
                max_prompt_length = batch_token_buckets_valid[b_id]//2,
            )
            trainer.train()
            state.wait_for_everyone()
            optimizer = trainer.optimizer
            save_model_path = model_path+f'_{args.adapter_type.lower()}'
            trainer.model.save_pretrained(save_model_path)
            if astate.is_local_main_process:
                tokenizer.save_pretrained(save_model_path)
            if (epoch_num+1) == total_epochs and batch_token_buckets_valid[b_id]==(args.BUCKET_MIN_SIZE):
                print(trainer.evaluate())
                break
            torch.cuda.empty_cache()
state.wait_for_everyone()

#%%
if args.rl:
    optimizer = None
    total_epochs = training_config_dict['training_arguments']['num_train_epochs']
    initial_lr = training_config_dict['training_arguments']['learning_rate']
    end_lr = 0.
    lr_buckets = np.linspace(initial_lr, end_lr, total_epochs+1)
    b_sz = training_config_dict['training_arguments']['per_device_train_batch_size']
    
    reward_fn_strings = []
    reward_fn_list = dataset_obj.format_rewards(training_config_dict['trl_arguments']['grpo_reward_funcs'])
    print(train_dataset)
    print(batch_token_buckets_valid)
    print('VLLM Server: https://{}:{}'.format(os.getenv('VLLM_SERVER_HOST'), os.getenv('VLLM_SERVER_PORT')))
    train_dataset['train'] = concatenate_datasets([train_dataset[str(k)] for k in batch_token_buckets_valid])
    if 'test' in dataset_obj.iterable_dataset_traineval.keys():
        train_dataset["test"] = dataset_obj.iterable_dataset_traineval['test']
    else:
        train_dataset["test"] = None
    
    training_arguments = GRPOConfig(
        max_prompt_length = args.max_seq_len-training_config_dict['trl_arguments']['grpo_max_completion_length'],
        num_generations = training_config_dict['trl_arguments']['grpo_num_generations'],
        max_completion_length = training_config_dict['trl_arguments']['grpo_max_completion_length'],
        beta = training_config_dict['trl_arguments']['grpo_beta'],
        reward_weights = training_config_dict['trl_arguments']['grpo_reward_weights'],
        temperature = float(os.getenv('VLLM_TEMPERATURE')),
        top_p = float(os.getenv('VLLM_TOP_P')),
        output_dir = training_config_dict['training_arguments']['output_dir'],
        num_train_epochs = training_config_dict['training_arguments']['num_train_epochs'],
        evaluation_strategy = "steps" if train_dataset['test'] is not None else "no",
        eval_steps = training_config_dict['training_arguments']['eval_steps'] if train_dataset['test'] is not None else 0,
        per_device_train_batch_size = b_sz,
        per_device_eval_batch_size = b_sz,
        gradient_accumulation_steps = training_config_dict['training_arguments']['gradient_accumulation_steps'],
        optim = training_config_dict['training_arguments']['optim'],
        warmup_ratio = training_config_dict['training_arguments']['warmup_ratio'],
        lr_scheduler_type = training_config_dict['training_arguments']['scheduler'],
        learning_rate = training_config_dict['training_arguments']['learning_rate'],
        weight_decay = training_config_dict['training_arguments']['weight_decay'],
        max_grad_norm = training_config_dict['training_arguments']['max_grad_norm'],
        vllm_server_host = os.getenv('VLLM_SERVER_HOST'),
        vllm_server_port = os.getenv('VLLM_SERVER_PORT'),
        use_vllm = True,
        log_completions = True,
        fp16 = False,
        bf16 = True,
        eval_on_start = True,
        gradient_checkpointing = False,
        seed = training_config_dict['training_arguments']['seed'],
        logging_steps = training_config_dict['training_arguments']['logging_steps'],
        report_to = training_config_dict['training_arguments']['report_to'],
        logging_dir = join(args.cache_dir, 'logging', datetime.now().strftime("%Y%m%d_%H%M%S")),
        save_steps = 0,
    )
    trainer = GRPOTrainer(
        model = model,
        reward_funcs = reward_fn_list,
        args = training_arguments,
        train_dataset = train_dataset["train"],
        eval_dataset = train_dataset["test"],
        processing_class = dataset_obj.tokenizer,
    )
    trainer.train()
    state.wait_for_everyone()
    optimizer = trainer.optimizer
    save_model_path = model_path+f'_{args.adapter_type.lower()}'
    trainer.model.save_pretrained(save_model_path)
    if astate.is_local_main_process:
        tokenizer.save_pretrained(save_model_path)
    print(trainer.evaluate())
    torch.cuda.empty_cache()
state.wait_for_everyone()

#%%
if args.save_merged:
    if astate.is_local_main_process:
        if isdir(join(model_path+f'_{args.adapter_type.lower()}', 'merged')):
            os.system('rm -r "{}"'.format(join(model_path+f'_{args.adapter_type.lower()}', 'merged')))
        tokenizer.save_pretrained(join(model_path+f'_{args.adapter_type.lower()}', 'merged'))
    model = model.merge_and_unload()
    model.save_pretrained(join(model_path+f'_{args.adapter_type.lower()}', 'merged'), safe_serialization=True)
    print("Model saved at '{}'".format(join(model_path+f'_{args.adapter_type.lower()}', 'merged')))
# del model
# torch.cuda.empty_cache()
state.wait_for_everyone()
accelerator.free_memory()

#%%
if args.save_gguf is not None:
    from unsloth import FastLanguageModel
    assert args.save_merged
    model_dir = join(model_path+f'_{args.adapter_type.lower()}', 'merged')
    if not isdir(args.model_name):
        assert isdir(args.cache_dir)
        base_path = join(args.cache_dir, args.model_name.replace('/', '-') + f'_gguf_{args.save_gguf}')
    else:
        base_path = args.model_name + f'_gguf_{args.save_gguf}'
    if astate.is_local_main_process:
        model, tokenizer = FastLanguageModel.from_pretrained(model_dir)
        model.save_pretrained_gguf(base_path, tokenizer, quantization_method = args.save_gguf)
        print(f"GGUF saved at '{base_path}'")