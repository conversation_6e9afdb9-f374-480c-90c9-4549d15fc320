#!/usr/bin/env python3
"""
Check Python version compatibility for IDSL LLM Framework
"""

import sys

def check_python_version():
    """Check if Python version is compatible with the framework"""
    
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    
    print(f"🐍 Current Python version: {version_str}")
    
    # Check if version is supported
    if version.major != 3:
        print("❌ Python 3.x is required")
        return False
    
    if version.minor == 13:
        print("❌ Python 3.13 is NOT supported!")
        print("   Python 3.13 has compatibility issues with:")
        print("   - Unsloth")
        print("   - vLLM") 
        print("   - Many other ML packages")
        print("")
        print("✅ Recommended: Use Python 3.12, 3.11, 3.10, or 3.9")
        return False
    
    if version.minor in [9, 10, 11, 12]:
        if version.minor == 12:
            print("✅ Python 3.12 - Excellent choice! (Recommended)")
        elif version.minor == 11:
            print("✅ Python 3.11 - Good choice!")
        elif version.minor == 10:
            print("✅ Python 3.10 - Good choice!")
        elif version.minor == 9:
            print("✅ Python 3.9 - Supported (but consider upgrading)")
        return True
    
    if version.minor < 9:
        print(f"❌ Python 3.{version.minor} is too old")
        print("   Minimum supported version: Python 3.9")
        return False
    
    if version.minor > 13:
        print(f"⚠️  Python 3.{version.minor} is newer than tested versions")
        print("   This may work but hasn't been tested")
        print("   Recommended: Use Python 3.12")
        return True
    
    return False

def main():
    print("🚀 IDSL LLM Framework - Python Version Check")
    print("=" * 50)
    
    if check_python_version():
        print("")
        print("🎉 Your Python version is compatible!")
        print("You can proceed with the installation:")
        print("   pip install -r requirements.txt")
    else:
        print("")
        print("💡 To install a compatible Python version:")
        print("   conda create -n idsl_llm python=3.12 -y")
        print("   conda activate idsl_llm")
        sys.exit(1)

if __name__ == "__main__":
    main()
