# IDSL LLM Framework Installation Guide

## Quick Setup (Recommended)

### Prerequisites
- **Anaconda or Miniconda** installed
- **Python 3.12** (recommended), 3.11, 3.10, or 3.9
- **⚠️ Python 3.13 is NOT supported** (incompatible with Unsloth, vLLM, and other packages)
- **CUDA-compatible GPU** (optional but recommended for training)

### Automatic Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd IDSL_LLM_Framework
   ```

2. **Run the setup script:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Activate the environment:**
   ```bash
   conda activate idsl_llm
   ```

4. **Test the installation:**
   ```bash
   python test_framework.py
   ```

## Manual Installation

### Step 1: Create Conda Environment

```bash
# Create environment from file
conda env create -f environment.yml

# OR create manually
conda create -n idsl_llm python=3.12 -y
conda activate idsl_llm
```

### Step 2: Install Core Dependencies

```bash
# Install core ML libraries
pip install torch>=2.7.0 transformers>=4.53.0 datasets>=3.6.0
pip install accelerate>=1.8.0 peft>=0.15.0 trl>=0.19.0

# Install quantization libraries
pip install bitsandbytes>=0.42.0 safetensors>=0.5.0

# Install all requirements
pip install -r requirements.txt
```

### Step 3: Install Optional Dependencies

```bash
# For development and advanced features
pip install -r requirements-dev.txt

# For Unsloth (single-GPU optimization) - install separately to avoid conflicts
pip install -r requirements-unsloth.txt
# OR manually:
# pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"

# For DeepSpeed (multi-GPU training)
pip install deepspeed>=0.10.0

# For Axolotl (advanced training)
pip install axolotl
```

## Platform-Specific Instructions

### macOS (Apple Silicon)

```bash
# Use conda-forge for better ARM64 support
conda install -c conda-forge pytorch transformers

# Some packages may need special handling
pip install --no-deps bitsandbytes
```

### Linux with CUDA

```bash
# Install PyTorch with CUDA support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install Flash Attention (if supported)
pip install flash-attn --no-build-isolation
```

### Windows

```bash
# Use conda for better Windows compatibility
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# Install remaining packages
pip install -r requirements.txt
```

## Verification

### Test Basic Functionality
```bash
python test_framework.py
```

### Test Training Pipeline
```bash
# Test with dynamic batching
./cli.sh dynamic sft --training_config unsloth_sft_config.yml --cache_dir ./test_cache

# Test Jupyter notebooks
jupyter notebook test_dataset_wrapper.ipynb
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size in config files
   - Use gradient accumulation
   - Enable gradient checkpointing

2. **Unsloth Installation Fails**
   - Skip Unsloth and use standard training
   - Try installing without dependencies: `pip install --no-deps unsloth`

3. **Import Errors**
   - Ensure conda environment is activated
   - Reinstall problematic packages: `pip install --force-reinstall <package>`

4. **Permission Errors**
   - Make scripts executable: `chmod +x cli.sh setup.sh`

### Environment Variables

```bash
# For better performance
export CUDA_VISIBLE_DEVICES=0,1  # Specify GPUs
export TOKENIZERS_PARALLELISM=false  # Avoid warnings
export HF_HOME=/path/to/cache  # Set HuggingFace cache directory
```

## Next Steps

1. **Configure your training:** Edit `unsloth_sft_config.yml` or other config files
2. **Prepare your dataset:** Create a custom dataset wrapper in `dataset_wrappers/`
3. **Start training:** Use `./cli.sh` with your preferred backend
4. **Monitor progress:** Use TensorBoard or Weights & Biases

For detailed usage instructions, see the main README.md file.
