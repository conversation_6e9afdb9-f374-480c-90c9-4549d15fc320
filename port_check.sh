#!/bin/bash

# Function to check if a port is in use
check_port() {
  local port=$1

  # Check if the port is in use
  if netstat -tuln | grep -q ":$port"; then
    return 0  # Port is in use
  else
    return 1  # Port is available
  fi
}

# Main script logic
if [ $# -ne 1 ]; then
  echo "Usage: $0 <port_number>"
  exit 1
fi

port=$1

check_port $port

if [ $? -eq 0 ]; then
  echo 0  # Port is in use
else
  echo 1  # Port is available
fi