{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "from os import listdir\n", "from os.path import isfile, isdir, join\n", "from tqdm.notebook import tqdm\n", "os.environ['LD_LIBRARY_PATH'] = '/usr/lib/x86_64-linux-gnu'\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "os.environ['CUDA_VISIBLE_DEVICES'] = '1'\n", "os.environ[\"UNSLOTH_RETURN_LOGITS\"] = \"1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle as pkl\n", "import math, random\n", "import cv2, imutils\n", "import json, yaml\n", "import multiprocessing\n", "import argparse\n", "import importlib\n", "from pprint import PrettyPrinter as pp\n", "from argparse import Namespace\n", "from datetime import datetime\n", "from PIL import Image\n", "from typing import Iterable, Callable, Dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.tensorboard import SummaryWriter\n", "from torch.utils.data import DataLoader, Dataset\n", "from torch.nn.utils.rnn import pad_sequence\n", "from torchinfo import summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unsloth import FastLanguageModel\n", "from unsloth import is_bfloat16_supported\n", "from unsloth.chat_templates import train_on_responses_only\n", "from transformers import get_linear_schedule_with_warmup, pipeline, TextStreamer\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig\n", "from transformers import GPTQConfig, TrainerCallback, DataCollatorForLanguageModeling, DataCollatorForSeq2Seq\n", "from transformers import BitsAndBytesConfig, TrainingArguments, EvalPrediction, logging\n", "from tokenizers import tokenizers\n", "from evaluate import load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import huggingface_hub\n", "import accelerate\n", "import bitsandbytes as bnb\n", "from trl import SFTTrainer\n", "from peft import LoraConfig, LoftQConfig, PeftModel, TaskType, get_peft_model, prepare_model_for_kbit_training\n", "from trainer.rope_scaling import ROPE_CONFIG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"--training_config\" not in sys.argv or 'DEFAULT' in sys.argv:\n", "    sys.argv = [\n", "        'script.py',\n", "        '--training_config', './unsloth_sft_config.yml',\n", "        '--cache_dir', '/data/server_cache',\n", "        '--max_seq_len', '16384',\n", "        '--adapter_type', 'LoRA',\n", "        '--quant_bits', '4',\n", "        '--gpu_parallel', '1',\n", "        '--lora_rank', '16',\n", "        '--lora_dropout', '0',\n", "        '--save_gguf', 'q4_k_m',\n", "    ]\n", "\n", "pprinter = pp(indent=2)\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--training_config', type=str, required=True)\n", "parser.add_argument('--cache_dir', type=str, default='../server_cache')\n", "parser.add_argument('--model_name', type=str, default=\"meta-llama/Llama-3.2-3B-Instruct\")\n", "parser.add_argument('--lora_rank', type=int, required=False, default=16)\n", "parser.add_argument('--lora_alpha', type=int, required=False, default=0)\n", "parser.add_argument('--lora_dropout', type=float, required=False, default=0.1)\n", "parser.add_argument('--gpu_parallel', type=int, required=False, default=1)\n", "parser.add_argument('--max_seq_len', type=int, required=False, default=32768)\n", "parser.add_argument('--quant_bits', type=int, required=False, default=4, choices=[4, 8])\n", "parser.add_argument('--adapter_type', type=str, required=False, default='LoRA', choices=['LoRA', 'QLoRA', 'LoftQ'])\n", "parser.add_argument('--save_gguf', type=str, required=False, default='')\n", "parser.add_argument('--save_merged', action='store_true', required=False)\n", "parser.add_argument('--push_to_hub', action='store_true', required=False)\n", "args = parser.parse_args()\n", "with open(args.training_config, 'r') as file:\n", "    training_config_dict = yaml.safe_load(file)\n", "\n", "if not isdir(args.cache_dir):\n", "    os.mkdir(args.cache_dir)\n", "assert isdir(args.cache_dir)\n", "if 'model_name' in training_config_dict['hub_config'].keys():\n", "    args.model_name = training_config_dict['hub_config']['model_name'] if training_config_dict['hub_config']['model_name'] is not None else args.model_name\n", "print(args)\n", "pprinter.pprint(training_config_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device = \"cuda:0\"\n", "token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else os.environ['HF_TOKEN']\n", "torch_device = torch.device(device)\n", "huggingface_hub.login(token=token, add_to_git_credential=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.quant_bits==4:\n", "    bnb_config_dict = {\n", "        'bnb_load_in_4bit': True,\n", "        'bnb_4bit_use_double_quant': True,\n", "        'bnb_4bit_quant_type': 'nf4',\n", "        'bnb_4bit_compute_dtype': torch.bfloat16,\n", "    }\n", "    quantization_config = BitsAndBytesConfig(load_in_4bit = bnb_config_dict['bnb_load_in_4bit'], \n", "                                            bnb_4bit_use_double_quant = bnb_config_dict['bnb_4bit_use_double_quant'],\n", "                                            bnb_4bit_quant_type = bnb_config_dict['bnb_4bit_quant_type'],\n", "                                            bnb_4bit_compute_dtype = bnb_config_dict['bnb_4bit_compute_dtype'],\n", "                                            )\n", "if args.quant_bits==8:\n", "    quantization_config = BitsAndBytesConfig(load_in_8bit=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trust_remote_code = training_config_dict['hub_config']['trust_remote_code']\n", "if not isdir(join(args.cache_dir, args.model_name)):\n", "    model_path = join(args.cache_dir, args.model_name.replace('/', '--'))\n", "    if not isdir(model_path):\n", "        huggingface_hub.snapshot_download(repo_id=args.model_name, local_dir=model_path)\n", "        model_config = AutoConfig.from_pretrained(\n", "            args.model_name,\n", "            token = token,\n", "            trust_remote_code = trust_remote_code,\n", "        )\n", "        pprinter.pprint(model_config)\n", "else:\n", "    model_path = join(args.cache_dir, args.model_name)\n", "\n", "print(model_path)\n", "model_config = AutoConfig.from_pretrained(model_path)\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = model_path,\n", "    max_seq_length = args.max_seq_len,\n", "    dtype = torch.bfloat16,\n", "    load_in_4bit = True if args.quant_bits==4 else False,\n", "    token = token,\n", "    trust_remote_code = trust_remote_code,\n", "    gpu_memory_utilization = 0.7,\n", "    fix_tokenizer = True,\n", "    device_map = \"sequential\",\n", "    quantization_config = quantization_config,\n", "    # rope_scaling = ROPE_CONFIG['rope_scaling_llama3'],\n", ")\n", "print(model_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                    \"gate_proj\", \"up_proj\", \"down_proj\",]\n", "if args.adapter_type in ['QLoRA']:\n", "    target_modules = []\n", "    for name, module in model.named_modules():\n", "        if isinstance(module, nn.Linear) and name!='lm_head':\n", "            target_modules.append(name)\n", "            print(name)\n", "\n", "model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = args.lora_rank, \n", "    target_modules = target_modules,\n", "    lora_alpha = args.lora_alpha if args.lora_alpha else args.lora_rank,\n", "    lora_dropout = args.lora_dropout, \n", "    bias = \"none\", \n", "    use_gradient_checkpointing = \"unsloth\", \n", "    random_state = training_config_dict['training_arguments']['seed'],\n", "    use_rslora = False, \n", "    loftq_config = None if args.adapter_type!='LoftQ' else LoftQConfig(loftq_bits=args.quant_bits, loftq_iter=5),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset_args = training_config_dict['dataset_arguments']\n", "dataset_dict = {}\n", "if dataset_args['dataset_split'] is not None:\n", "    dataset_dict['split_name'] = dataset_args['dataset_split']\n", "if dataset_args['dataset_subset'] is not None:\n", "    dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                cache_dir=dataset_args['cache_dir'], \n", "                                test_size=dataset_args['test_split'], \n", "                                **dataset_dict,\n", "                            )\n", "tokenizer = dataset_obj.format_dataset(tokenizer=tokenizer, chat_template=dataset_args['chat_template'])\n", "print('Dataset Loaded.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bleu_metric = load(\"evaluate-metric/bleu\", use_auth_token=token)\n", "rouge_metric = load(\"evaluate-metric/rouge\", use_auth_token=token)\n", "meteor_metric = load(\"evaluate-metric/meteor\", use_auth_token=token)\n", "\n", "def preprocess_logits_for_metrics(logits, labels):\n", "    pred_ids = torch.argmax(logits, dim=-1)\n", "    return pred_ids, labels\n", "\n", "def compute_metrics(p: EvalPrediction):\n", "    print(\"in compute\")\n", "    logits, labels = p\n", "    if isinstance(logits, tuple):\n", "        logits = logits[0]\n", "    if isinstance(logits, np.ndarray):\n", "        logits = torch.from_numpy(logits)\n", "    if isinstance(labels, np.ndarray):\n", "        labels = torch.from_numpy(labels)\n", "    preds = logits.detach().cpu()\n", "    labels = labels.detach().cpu()\n", "\n", "    preds[preds == -100] = dataset_obj.tokenizer.pad_token_id\n", "    labels[labels == -100] = dataset_obj.tokenizer.pad_token_id\n", "    preds_list = preds.tolist()\n", "    labels_list = labels.tolist()\n", "\n", "    decoded_preds = dataset_obj.tokenizer.batch_decode(preds_list, skip_special_tokens=True)\n", "    decoded_labels = dataset_obj.tokenizer.batch_decode(labels_list, skip_special_tokens=True)\n", "    decoded_labels = [[label] for label in decoded_labels]\n", "\n", "    bleu = bleu_metric.compute(predictions=decoded_preds, references=decoded_labels)\n", "    rouge = rouge_metric.compute(predictions=decoded_preds, references=decoded_labels)\n", "    meteor = meteor_metric.compute(predictions=decoded_preds, references=decoded_labels)\n", "\n", "    return {\n", "        \"bleu\": bleu[\"bleu\"],\n", "        **rouge,\n", "        **meteor,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not isdir(join(args.cache_dir, 'logging')):\n", "    os.mkdir(join(args.cache_dir, 'logging'))\n", "training_args = TrainingArguments(\n", "    fp16 = not is_bfloat16_supported(),\n", "    bf16 = is_bfloat16_supported(),\n", "    eval_strategy= \"steps\" if 'test' in dataset_obj.iterable_dataset_traineval.keys() else 'no',\n", "    logging_dir= join(args.cache_dir, 'logging', datetime.now().strftime(\"%Y%m%d_%H%M%S\")),\n", "    **training_config_dict['training_arguments'],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    trainer = SFTT<PERSON>er(\n", "        model = model,\n", "        tokenizer = dataset_obj.tokenizer,\n", "        train_dataset = dataset_obj.iterable_dataset_traineval['train'],\n", "        eval_dataset = dataset_obj.iterable_dataset_traineval['test'] if 'test' in dataset_obj.iterable_dataset_traineval.keys() else None,\n", "        dataset_text_field = \"text\",\n", "        max_seq_length = args.max_seq_len,\n", "        data_collator = DataCollatorForSeq2Seq(tokenizer = dataset_obj.tokenizer),\n", "        dataset_num_proc = multiprocessing.cpu_count(),\n", "        packing = False, \n", "        compute_metrics = compute_metrics,\n", "        preprocess_logits_for_metrics = preprocess_logits_for_metrics,\n", "        args = training_args,\n", "    )\n", "    trainer = train_on_responses_only(\n", "        trainer,\n", "        instruction_part = dataset_args[\"user_header\"],\n", "        response_part = dataset_args[\"assistant_header\"],\n", "    )\n", "except Exception as e:\n", "    trainer = SFTT<PERSON>er(\n", "        model = model,\n", "        tokenizer = dataset_obj.tokenizer,\n", "        train_dataset = dataset_obj.iterable_dataset_traineval['train'],\n", "        eval_dataset = dataset_obj.iterable_dataset_traineval['test'] if 'test' in dataset_obj.iterable_dataset_traineval.keys() else None,\n", "        dataset_text_field = \"text\",\n", "        max_seq_length = args.max_seq_len,\n", "        dataset_num_proc = multiprocessing.cpu_count(),\n", "        packing = False, \n", "        compute_metrics = compute_metrics,\n", "        preprocess_logits_for_metrics = preprocess_logits_for_metrics,\n", "        args = training_args,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Saving', model_path+'_adapter')\n", "model.save_pretrained(model_path+'_adapter')\n", "tokenizer.save_pretrained(model_path+'_adapter')\n", "if args.push_to_hub:\n", "    model.push_to_hub(f\"{training_config_dict['hub_config']['username']}/{args.model_name.replace('/', '-')}_adapter\", \n", "                    token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else token)\n", "    tokenizer.push_to_hub(f\"{training_config_dict['hub_config']['username']}/{args.model_name.replace('/', '-')}_adapter\",\n", "                    token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else token)\n", "\n", "if args.save_merged:\n", "    print('Saving', join(model_path+'_adapter', 'merged'))\n", "    model.save_pretrained_merged(join(model_path+'_adapter', 'merged'), tokenizer, save_method = \"lora\")\n", "    if args.save_gguf:\n", "        print('Saving', model_path+f'_gguf_{args.save_gguf}')\n", "        model.save_pretrained_gguf(model_path+f'_gguf_{args.save_gguf}', tokenizer, quantization_method = args.save_gguf)\n", "        if args.push_to_hub:\n", "            model.push_to_hub_gguf(f\"{training_config_dict['hub_config']['username']}/{args.model_name.replace('/', '-')}_gguf_{args.save_gguf}\", tokenizer, quantization_method = args.save_gguf, \n", "                            token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else token)\n", "    if args.quant_bits==4:\n", "        print('Saving', model_path+'_qlora_4bit')\n", "        model.save_pretrained_merged(model_path+'_qlora_4bit', tokenizer, save_method = \"merged_4bit_forced\")\n", "        if args.push_to_hub:\n", "            model.push_to_hub_merged(f\"{training_config_dict['hub_config']['username']}/{args.model_name.replace('/', '-')}_qlora_4bit\", tokenizer,\n", "                    token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else token)\n", "    if args.quant_bits!=4:\n", "        print('Saving', model_path+'_lora_16bit')\n", "        model.save_pretrained_merged(model_path+'_lora_16bit', tokenizer, save_method = \"merged_16bit\")\n", "        if args.push_to_hub:\n", "            model.push_to_hub_merged(f\"{training_config_dict['hub_config']['username']}/{args.model_name.replace('/', '-')}_lora_16bit\", tokenizer,\n", "                    token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else token)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sygen_conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}