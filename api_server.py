#!/usr/bin/env python3
"""
IDSL LLM Framework API Server
Production-ready FastAPI server for LLM training and inference
"""

import os
import sys
import json
import uuid
import asyncio
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

import yaml
from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
import uvicorn

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Global state management
training_jobs = {}
inference_servers = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    print("🚀 IDSL LLM Framework API Server starting...")
    yield
    # Shutdown
    print("🛑 IDSL LLM Framework API Server shutting down...")
    # Clean up any running processes
    for job_id, job_info in training_jobs.items():
        if job_info.get('process') and job_info['process'].poll() is None:
            job_info['process'].terminate()

app = FastAPI(
    title="IDSL LLM Framework API",
    description="Production API for LLM training and inference",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)