hub_config:
  username: "rg1995007"
  hf_token:
  trust_remote_code: true
  model_name: "meta-llama/Llama-3.2-3B-Instruct"

dataset_arguments:
  test_split: 0.1
  dataset_type: "ultrafeedback"
  dataset_name: "argilla/ultrafeedback-binarized-preferences-cleaned"
  dataset_subset:
  dataset_split:
  cache_dir: "/data/server_cache"
  chat_template: "llama-3.2"
  field_messages: "prompt"
  field_chosen: "chosen"
  field_rejected: "rejected"
  test_field_messages: "conversations"
  roles_to_train: ["assistant"]
  train_on_eos: last
  system_header: "<|start_header_id|>system<|end_header_id|>\n\n"
  user_header: "<|start_header_id|>user<|end_header_id|>\n\n"
  assistant_header: "<|start_header_id|>assistant<|end_header_id|>\n\n"

training_arguments:
  dpo_use_weighting: 
  rpo_alpha: 
  per_device_train_batch_size: 16
  per_device_eval_batch_size: 16
  gradient_accumulation_steps: 4
  eval_steps: 0.25
  warmup_ratio: 0.1
  num_train_epochs: 1
  learning_rate: 0.00005
  max_grad_norm: 1.0
  logging_steps: 100
  weight_decay: 0.01
  seed: 3407
  save_steps: 1
  save_total_limit: 2
  optim: "adamw_bnb_8bit"
  lr_scheduler_type: "cosine"
  output_dir: "/data/server_cache/outputs"
  report_to: "tensorboard"
  model_type: AutoModelForCausalLM
  tokenizer_type: AutoTokenizer