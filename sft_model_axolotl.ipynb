{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "from os import listdir\n", "from os.path import isfile, isdir, join\n", "from tqdm.notebook import tqdm\n", "os.environ['LD_LIBRARY_PATH'] = '/usr/lib/x86_64-linux-gnu'\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle as pkl\n", "import math, random\n", "import cv2, imutils\n", "import json, string, yaml\n", "import shutil, gc, copy\n", "import multiprocessing\n", "import subprocess, asyncio\n", "import argparse\n", "import importlib\n", "from pprint import PrettyPrinter as pp\n", "from argparse import Namespace\n", "from datetime import datetime\n", "from PIL import Image\n", "from typing import Iterable, Callable, Dict, Literal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.tensorboard import SummaryWriter\n", "from torch.utils.data import DataLoader, Dataset\n", "from torch.nn.utils.rnn import pad_sequence\n", "from torchinfo import summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import get_linear_schedule_with_warmup, pipeline, TextStreamer\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig\n", "from transformers import GPTQConfig, TrainerCallback, DataCollatorForLanguageModeling, DataCollatorForSeq2Seq\n", "from transformers import BitsAndBytesConfig, TrainingArguments, logging\n", "from tokenizers import tokenizers\n", "from evaluate import load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import huggingface_hub\n", "import accelerate\n", "import bitsandbytes as bnb\n", "from trl import SFTTrainer\n", "from peft import LoraConfig, LoftQConfig, PeftModel, TaskType, get_peft_model, prepare_model_for_kbit_training\n", "from trainer.rope_scaling import ROPE_CONFIG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["axolotl_config = \"\"\"\n", "base_model: {base_model}\n", "tokenizer_config: {tokenizer_config}\n", "model_type: {model_type}\n", "tokenizer_type: {tokenizer_type}\n", "trust_remote_code: {trust_remote_code}\n", "\n", "bnb_config_kwargs:\n", "  bnb_4bit_quant_type: nf4\n", "  bnb_4bit_use_double_quant: {bnb_4bit_use_double_quant}\n", "  llm_int8_has_fp16_weight: {llm_int8_has_fp16_weight}\n", "  \n", "load_in_8bit: {load_in_8bit}\n", "load_in_4bit: {load_in_4bit}\n", "bf16: auto\n", "fp16: {fp16}\n", "bfloat16: {bfloat16}\n", "float16: {float16}\n", "\n", "chat_template: jinja\n", "chat_template_jinja: {chat_template_jinja}\n", "sequence_len: {sequence_len}\n", "sample_packing: false\n", "pad_to_sequence_len: false\n", "\n", "datasets:\n", "  - path: {dataset_path}\n", "    train_on_split: train\n", "    type: chat_template\n", "    chat_template: jinja\n", "    chat_template_jinja: {dataset_chat_template_jinja}\n", "    field_messages: {field_messages}\n", "    roles_to_train: {roles_to_train}\n", "    train_on_eos: {train_on_eos} # all | turn | last\n", "    roles:\n", "      user: [\"user\"]\n", "      assistant: [\"assistant\"]\n", "      system: [\"system\"]\n", "      tool: [\"tool\"]\n", "\n", "test_datasets:\n", "  - path: {test_dataset_path}\n", "    split: test\n", "    type: chat_template\n", "    chat_template: jinja\n", "    chat_template_jinja: {test_dataset_chat_template_jinja}\n", "    field_messages: {test_field_messages}\n", "    roles:\n", "      user: [\"user\"]\n", "      assistant: [\"assistant\"]\n", "      system: [\"system\"]\n", "      tool: [\"tool\"]\n", "\n", "output_dir: {output_dir}\n", "dataset_prepared_path: {dataset_prepared_path}\n", "hub_model_id: {hub_model_id}\n", "hub_strategy: {hub_strategy}\n", "\n", "adapter: {adapter}\n", "lora_model_dir: {lora_model_dir}\n", "lora_r: {lora_r}\n", "lora_alpha: {lora_alpha}\n", "lora_dropout: {lora_dropout}\n", "lora_target_linear: {lora_target_linear}\n", "lora_target_modules:\n", "  {lora_target_modules}\n", "\n", "lora_fan_in_fan_out:\n", "{loftq_bits}\n", "\n", "use_tensorboard: true\n", "gradient_accumulation_steps: {gradient_accumulation_steps}\n", "micro_batch_size: {micro_batch_size}\n", "eval_batch_size: {eval_batch_size}\n", "num_epochs: {num_epochs}\n", "warmup_ratio: {warmup_ratio}\n", "learning_rate: {learning_rate}\n", "weight_decay: {weight_decay}\n", "max_grad_norm: {max_grad_norm}\n", "eval_steps: {eval_steps}\n", "save_steps: {save_steps}\n", "eval_strategy: {eval_strategy}\n", "save_total_limit: {save_total_limit}\n", "max_steps: {max_steps}\n", "logging_steps: {logging_steps}\n", "\n", "eval_causal_lm_metrics: [\"sacrebleu\", \"comet\", \"ter\", \"chrf\", \"perplexity\"]\n", "auto_find_batch_size: {auto_find_batch_size}\n", "save_safetensors: {save_safetensors}\n", "train_on_inputs: false\n", "group_by_length: true\n", "gradient_checkpointing: true\n", "\n", "dataset_prepared_path: {dataset_prepared_path}\n", "early_stopping_patience: {early_stopping_patience}\n", "lr_scheduler: {lr_scheduler}\n", "optimizer: {optimizer}\n", "\n", "special_tokens:\n", "\n", "flash_attention: true\n", "deepspeed: {deepspeed}\n", "debug: false\n", "seed: 42\n", "strict: false\n", "use_tensorboard: {use_tensorboard}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def in_notebook() -> bool:\n", "    try:\n", "        # Try to get the name of the current shell.\n", "        shell = get_ipython().__class__.__name__\n", "        if shell == 'ZMQInteractiveShell':\n", "            # <PERSON><PERSON><PERSON> notebook or qtconsole\n", "            return True\n", "        elif shell == 'TerminalInteractiveShell':\n", "            # Terminal running IPython\n", "            return False\n", "        else:\n", "            # Other type (unknown)\n", "            return False\n", "    except NameError:\n", "        # get_ipython is not defined, likely not running in IPython at all.\n", "        return False\n", "if in_notebook() or \"--training_config\" not in sys.argv or 'DEFAULT' in sys.argv:\n", "    sys.argv = [\n", "        'script.py',\n", "        '--training_config', './axolotl_sft_config.yml',\n", "        '--cache_dir', '/data/server_cache',\n", "        '--adapter_type', 'QLoRA',\n", "        '--quant_bits', '4',\n", "        '--gpu_parallel', '2',\n", "        '--lora_rank', '16',\n", "        '--lora_dropout', '0.05',\n", "        '--save_gguf', 'q4_k_m',\n", "        '--save_merged',\n", "    ]\n", "\n", "pprinter = pp(indent=2)\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--training_config', type=str, required=True)\n", "parser.add_argument('--accelerator_config', type=str, default='./accelerator_config.yml')\n", "parser.add_argument('--model_name', type=str, default='meta-llama/Llama-3.2-3B-Instruct')\n", "parser.add_argument('--cache_dir', type=str, default='../server_cache')\n", "parser.add_argument('--lora_rank', type=int, required=False, default=16)\n", "parser.add_argument('--lora_alpha', type=int, required=False, default=0)\n", "parser.add_argument('--lora_dropout', type=float, required=False, default=0.1)\n", "parser.add_argument('--gpu_parallel', type=int, required=False, default=1)\n", "parser.add_argument('--max_seq_len', type=int, required=False, default=32768)\n", "parser.add_argument('--quant_bits', type=int, required=False, default=4, choices=[4, 8])\n", "parser.add_argument('--adapter_type', type=str, required=False, default='LoRA', choices=['LoRA', 'QLoRA', 'LoftQ'])\n", "parser.add_argument('--save_gguf', type=str, required=False, default='')\n", "parser.add_argument('--save_merged', action='store_true', required=False)\n", "args = parser.parse_args()\n", "with open(args.training_config, 'r') as file:\n", "    training_config_dict = yaml.safe_load(file)\n", "with open(args.accelerator_config, 'r') as file:\n", "    accelerator_config_dict = yaml.safe_load(file)\n", "\n", "if not isdir(args.cache_dir):\n", "    os.mkdir(args.cache_dir)\n", "assert isdir(args.cache_dir)\n", "if 'model_name' in training_config_dict['hub_config'].keys():\n", "    args.model_name = training_config_dict['hub_config']['model_name'] if training_config_dict['hub_config']['model_name'] is not None else args.model_name\n", "print(args)\n", "pprinter.pprint(training_config_dict)\n", "pprinter.pprint(accelerator_config_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stream_command(command):\n", "    # Open the process with stdout piped and merge stderr into stdout.\n", "    process = subprocess.Popen(\n", "        command,\n", "        shell=True,\n", "        stdout=subprocess.PIPE,\n", "        stderr=subprocess.STDOUT,\n", "        text=True  # Use text mode to get strings instead of bytes.\n", "    )\n", "    \n", "    # Continuously read output line by line.\n", "    while True:\n", "        output = process.stdout.readline()\n", "        if output == '' and process.poll() is not None:\n", "            break\n", "        if output:\n", "            print(output.strip())\n", "    \n", "    # Optionally, return the command's exit code.\n", "    return process.poll()\n", "\n", "async def astream_command(command: str, polling_timeout: float = 5.0, chunk_size: int = 32):\n", "    # Start the subprocess asynchronously.\n", "    process = await asyncio.create_subprocess_shell(\n", "        command,\n", "        stdout=asyncio.subprocess.PIPE,\n", "        stderr=asyncio.subprocess.STDOUT\n", "    )\n", "    \n", "    # Continuously read output line by line.\n", "    while True:\n", "        try:\n", "            line = await asyncio.wait_for(process.stdout.read(chunk_size), timeout=polling_timeout)\n", "            if not line:\n", "                break\n", "            line = line.decode(\"utf-8\", errors='replace')\n", "        except asyncio.TimeoutError:\n", "            yield \"\"\n", "        except Exception as e:\n", "            line = None\n", "        finally:\n", "            if line is not None and type(line)==str:\n", "                yield line\n", "    \n", "    # Wait for the process to complete.\n", "    await process.wait()\n", "    yield \"<END_OF_STREAM>\"\n", "    \n", "async def run_astream_command(command: str):\n", "    print(command)\n", "    try:\n", "        async for ln in astream_command(command):\n", "            if ln==\"<END_OF_STREAM>\":\n", "                break\n", "            sys.stdout.write(ln)\n", "            sys.stdout.flush()\n", "    except Exception as e:\n", "        sys.stdout.write(f\"\\nStream ended with exception: {e}\\n\")\n", "        sys.stdout.flush()\n", "        return False\n", "    return True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token = os.environ['HF_TOKEN']\n", "huggingface_hub.login(token=token, add_to_git_credential=False)\n", "if args.quant_bits==4:\n", "    bnb_config_dict = {\n", "        'load_in_8bit': False,\n", "        'load_in_4bit': True,\n", "        'bnb_4bit_use_double_quant': True,\n", "        'llm_int8_has_fp16_weight': False,\n", "    }\n", "else:\n", "    bnb_config_dict = {\n", "        'load_in_8bit': True,\n", "        'load_in_4bit': False,\n", "        'bnb_4bit_use_double_quant': False,\n", "        'llm_int8_has_fp16_weight': False,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trust_remote_code = training_config_dict['hub_config']['trust_remote_code']\n", "if not isdir(join(args.cache_dir, args.model_name)):\n", "    model_path = join(args.cache_dir, args.model_name.replace('/', '--'))\n", "    if not isdir(model_path):\n", "        huggingface_hub.snapshot_download(repo_id=args.model_name, local_dir=model_path)\n", "        model_config = AutoConfig.from_pretrained(\n", "            args.model_name,\n", "            token = token,\n", "            trust_remote_code = trust_remote_code,\n", "        )\n", "        pprinter.pprint(model_config)\n", "else:\n", "    model_path = join(args.cache_dir, args.model_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset_args = training_config_dict['dataset_arguments']\n", "dataset_dict = {}\n", "if dataset_args['dataset_split'] is not None:\n", "    dataset_dict['split_name'] = dataset_args['dataset_split']\n", "if dataset_args['dataset_subset'] is not None:\n", "    dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                cache_dir=dataset_args['cache_dir'], \n", "                                test_size=dataset_args['test_split'], \n", "                                **dataset_dict,\n", "                            )\n", "tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(model_path), chat_template=dataset_args['chat_template'])\n", "dataset_path = join(dataset_args['cache_dir'], 'processed_axolotl_data')\n", "if isdir(dataset_path):\n", "    os.system('rm -r \"{}\"'.format(dataset_path))\n", "dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(dataset_obj.iterable_dataset_traineval)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["axolotl_config_parsed = list(string.Formatter.parse(\"\", axolotl_config))\n", "axolotl_config_keys = [f[1] for f in axolotl_config_parsed if f[1]]\n", "axolotl_config_dict = {f: '' for f in axolotl_config_keys}\n", "\n", "axolotl_config_dict['base_model'] = model_path\n", "axolotl_config_dict['model_type'] = training_config_dict['training_arguments']['model_type']\n", "axolotl_config_dict['tokenizer_type'] = training_config_dict['training_arguments']['tokenizer_type']\n", "axolotl_config_dict['output_dir'] = training_config_dict['training_arguments']['output_dir']\n", "axolotl_config_dict['trust_remote_code'] = 'true' if trust_remote_code else 'false'\n", "\n", "axolotl_config_dict['sequence_len'] = args.max_seq_len\n", "axolotl_config_dict['dataset_path'] = dataset_path\n", "axolotl_config_dict['field_messages'] = training_config_dict['dataset_arguments']['field_messages']\n", "axolotl_config_dict['roles_to_train'] = training_config_dict['dataset_arguments']['roles_to_train']\n", "axolotl_config_dict['train_on_eos'] = training_config_dict['dataset_arguments']['train_on_eos']\n", "axolotl_config_dict['test_dataset_path'] = dataset_path\n", "axolotl_config_dict['test_field_messages'] = training_config_dict['dataset_arguments']['field_messages']\n", "\n", "axolotl_config_dict['micro_batch_size'] = max(1, \n", "    training_config_dict['training_arguments']['per_device_train_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "axolotl_config_dict['eval_batch_size'] = max(1, \n", "    training_config_dict['training_arguments']['per_device_eval_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "axolotl_config_dict['gradient_accumulation_steps'] = training_config_dict['training_arguments']['gradient_accumulation_steps']\n", "axolotl_config_dict['num_epochs'] = training_config_dict['training_arguments']['num_train_epochs']\n", "axolotl_config_dict['warmup_ratio'] = training_config_dict['training_arguments']['warmup_ratio']\n", "axolotl_config_dict['learning_rate'] = training_config_dict['training_arguments']['learning_rate']\n", "axolotl_config_dict['weight_decay'] = training_config_dict['training_arguments']['weight_decay']\n", "axolotl_config_dict['max_grad_norm'] = training_config_dict['training_arguments']['max_grad_norm']\n", "axolotl_config_dict['eval_steps'] = training_config_dict['training_arguments']['eval_steps']\n", "axolotl_config_dict['save_steps'] = training_config_dict['training_arguments']['save_steps']\n", "axolotl_config_dict['logging_steps'] = training_config_dict['training_arguments']['logging_steps']\n", "axolotl_config_dict['save_total_limit'] = training_config_dict['training_arguments']['save_total_limit']\n", "axolotl_config_dict['lr_scheduler'] = training_config_dict['training_arguments']['lr_scheduler_type']\n", "axolotl_config_dict['optimizer'] = training_config_dict['training_arguments']['optim']\n", "axolotl_config_dict['use_tensorboard'] = 'true' if training_config_dict['training_arguments']['report_to']=='tensorboard' else 'false'\n", "axolotl_config_dict['eval_strategy'] = 'no' if dataset_args['test_split']==0 else ''\n", "axolotl_config_dict['deepspeed'] = join(os.getcwd(), 'deepspeed_configs/zero2.json')\n", "\n", "dataset_prepared_path = join(args.cache_dir, 'axolotl_prepared_dataset')\n", "if isdir(dataset_prepared_path):\n", "    os.system(f'rm -rf \"{dataset_prepared_path}\"')\n", "axolotl_config_dict['dataset_prepared_path'] = dataset_prepared_path\n", "\n", "if args.adapter_type=='LoRA':\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                    \"gate_proj\", \"up_proj\", \"down_proj\",]\n", "    axolotl_config_dict['adapter'] = 'lora'\n", "    axolotl_config_dict['lora_target_modules'] = '\\n  '.join([f'- {f}' for f in target_modules])\n", "if args.adapter_type=='QLoRA':\n", "    axolotl_config_dict['adapter'] = 'qlora'\n", "    axolotl_config_dict['lora_target_linear'] = 'true'\n", "if args.adapter_type=='LoftQ':\n", "    axolotl_config_dict['adapter'] = 'lora'\n", "    axolotl_config_dict['lora_target_linear'] = 'true'\n", "    axolotl_config_dict['loftq_bits'] = \"peft:\\n  loftq_config:\\n    loftq_bits: {}\".format(args.quant_bits)\n", "axolotl_config_dict['lora_r'] = args.lora_rank\n", "axolotl_config_dict['lora_alpha'] = args.lora_alpha if args.lora_alpha else args.lora_rank\n", "axolotl_config_dict['lora_dropout'] = args.lora_dropout\n", "\n", "for k in bnb_config_dict.keys():\n", "    axolotl_config_dict[k] = 'true' if bnb_config_dict[k] else 'false'\n", "\n", "axolotl_config_yml = yaml.safe_load(axolotl_config.format(**axolotl_config_dict))\n", "axolotl_config_yml['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "axolotl_config_yml['datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "axolotl_config_yml['test_datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "axolotl_config_yml['special_tokens'] = {'pad_token': dataset_obj.tokenizer.pad_token if dataset_obj.tokenizer.pad_token else dataset_obj.tokenizer.eos_token}\n", "axolotl_config_yml = yaml.dump(axolotl_config_yml, default_flow_style=False, sort_keys=False)\n", "axolotl_config_yml = axolotl_config_yml.replace('null', '')\n", "\n", "axolotl_config_path = join(training_config_dict['training_arguments']['output_dir'], f'{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.yml')\n", "if not isdir(training_config_dict['training_arguments']['output_dir']):\n", "    os.mkdir(training_config_dict['training_arguments']['output_dir'])\n", "with open(axolotl_config_path, 'w') as fp:\n", "    fp.write(axolotl_config_yml)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpu_count = ','.join([str(f) for f in range(accelerator_config_dict['num_processes'])])\n", "os.environ['TOKENIZERS_PARALLELISM'] = 'false'\n", "if in_notebook():\n", "    ! echo \"CUDA_VISIBLE_DEVICES: {gpu_count}\"\n", "    ! echo \"Config saved at: {axolotl_config_path}\"\n", "    ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.preprocess \"{axolotl_config_path}\" \n", "    ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.train \"{axolotl_config_path}\" \n", "    # ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" axolotl preprocess \"{axolotl_config_path}\" \n", "    # ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" axolotl train \"{axolotl_config_path}\" \n", "else:\n", "    asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.preprocess \"{axolotl_config_path}\"'))\n", "    asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.train \"{axolotl_config_path}\"'))\n", "\n", "if args.save_merged:\n", "    lora_model_dir = training_config_dict['training_arguments']['output_dir']\n", "    if in_notebook():\n", "        os.environ['AXOLOTL_CONFIG_PATH'] = axolotl_config_path\n", "        os.environ['LORA_MODEL_DIR'] = lora_model_dir\n", "        ! python3 -m axolotl.cli.merge_lora \"$AXOLOTL_CONFIG_PATH\" --lora_model_dir=\"$LORA_MODEL_DIR\"\n", "    else:\n", "        asyncio.run(run_astream_command(f'python3 -m axolotl.cli.merge_lora \"{axolotl_config_path}\" --lora_model_dir=\"{lora_model_dir}\"'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sygen_conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}