### **IDSL LLM FineTune Framework**

#### **Motivation**  
Existing LLM fine-tuning frameworks often abstract the training process through APIs, making it difficult for users to integrate custom datasets. This abstraction can lead to misconfigurations or unintended modifications to the tokenizer, potentially causing the model to lose its foundational knowledge and retrain from scratch.  

Our framework simplifies this process by introducing a **single-file data wrapper** that seamlessly integrates with the training API. Users only need to modify a single line in the training YAML configuration to specify the name of their custom data-wrapper module. This eliminates the need for cumbersome configuration files and prevents issues caused by complex data structures that can break during training. Beyond data integration, our framework is designed to **optimize resource utilization**, ensuring efficient training across various hardware configurations. We leverage **DeepSpeed ZeRO-2 acceleration** to distribute optimizer states, gradients, and parameters efficiently across GPUs, reducing memory overhead while maintaining high throughput. This approach allows users to fine-tune models on multiple GPUs without running into memory bottlenecks.

Additionally, our framework supports custom bit-level quantization using the BitsAndBytes (BnB) quantization framework, allowing for efficient model compression while maintaining performance. This can be seamlessly combined with LoRA (Low-Rank Adaptation) adapters, which enable parameter-efficient fine-tuning. Furthermore, fine-tuning can be further optimized with QLoRA (Quantized LoRA) and LoftQ (Low-Rank Optimization for Quantization), which improves quantization-aware training for enhanced accuracy and stability. These optimizations significantly reducing GPU memory overhead while maintaining high model performance.

#### **Key Features**  
1. **Optimized Single-GPU Training:** Leverages the **UnsLoth backend** for improved fine-tuning performance on a single GPU.  
2. **Efficient Multi-GPU Training:** Supports **DeepSpeed and Axolotl** for efficient multi-GPU scaling by reducing memory consumption and increasing training speed.  
3. **Scalable Performance:** Utilizes **Hugging Face Accelerate** to seamlessly distribute training across multiple devices, ensuring optimal performance.  
4. **Flexible Deployment:** Enables **CPU-GPU mixed precision inference** for fine-tuning with support for **llama.cpp, Ollama, and VLLM** backends.  
5. **Dynamic Batching Support:** Adjusts input batch sizes dynamically during training to **maximize GPU memory usage** while preventing **out-of-memory (OOM) errors**.  

This framework offers a streamlined, adaptable, and highly efficient approach to fine-tuning LLMs, catering to both novice and advanced users. It abstracts away the complexities of training setup and optimization, allowing users to focus solely on data integration rather than dealing with cumbersome configuration files. By simplifying fine-tuning workflows, the framework ensures that custom dataset integration requires only a single-line modification in the YAML configuration, eliminating the need for intricate hyperparameter tuning or manual optimization.