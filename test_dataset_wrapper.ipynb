{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "from os import listdir\n", "from os.path import isfile, isdir, join\n", "from tqdm.notebook import tqdm\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "os.environ['LD_LIBRARY_PATH'] = '/usr/lib/x86_64-linux-gnu'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle as pkl\n", "import math, random\n", "import cv2, imutils\n", "import json, string, yaml\n", "import multiprocessing\n", "import argparse\n", "import importlib\n", "from pprint import PrettyPrinter as pp\n", "from argparse import Namespace\n", "from datetime import datetime\n", "from PIL import Image\n", "from typing import Iterable, Callable, Dict"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig\n", "from dataset_wrappers.wrapper import sft_dataset, dpo_dataset, grpo_dataset"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Namespace(model_path='meta-llama/Llama-3.2-3B-Instruct', training_config='./unsloth_sft_config.yml', cache_dir='/data/server_cache', num_samples=5, sft=True, dpo=False, rl=False)\n", "{ 'dataset_arguments': { 'assistant_header': '<|start_header_id|>assistant<|end_header_id|>\\n'\n", "                                             '\\n',\n", "                         'cache_dir': '/data/server_cache',\n", "                         'chat_template': 'llama-3.2',\n", "                         'dataset_name': 'tatsu-lab/alpaca',\n", "                         'dataset_split': None,\n", "                         'dataset_subset': None,\n", "                         'dataset_type': 'alpaca',\n", "                         'system_header': '<|start_header_id|>system<|end_header_id|>\\n'\n", "                                          '\\n',\n", "                         'test_split': 0.1,\n", "                         'user_header': '<|start_header_id|>user<|end_header_id|>\\n'\n", "                                        '\\n'},\n", "  'hub_config': { 'hf_token': None,\n", "                  'model_name': 'meta-llama/Llama-3.2-3B-Instruct',\n", "                  'trust_remote_code': True,\n", "                  'username': 'rg1995007'},\n", "  'training_arguments': { 'eval_steps': 0.25,\n", "                          'gradient_accumulation_steps': 4,\n", "                          'learning_rate': 0.0001,\n", "                          'logging_steps': 1,\n", "                          'lr_scheduler_type': 'linear',\n", "                          'num_train_epochs': 1,\n", "                          'optim': 'adamw_8bit',\n", "                          'output_dir': '/data/server_cache/outputs',\n", "                          'per_device_eval_batch_size': 4,\n", "                          'per_device_train_batch_size': 16,\n", "                          'report_to': 'tensorboard',\n", "                          'save_steps': 100,\n", "                          'save_total_limit': 2,\n", "                          'seed': 3407,\n", "                          'warmup_ratio': 0.1,\n", "                          'weight_decay': 0.01}}\n"]}], "source": ["sys.argv = [\n", "    'script.py',\n", "    '--model_path', 'meta-llama/Llama-3.2-3B-Instruct',\n", "    '--training_config', './unsloth_sft_config.yml',\n", "    '--cache_dir', '/data/server_cache',\n", "    '--num_samples', '5',\n", "    '--sft',\n", "]\n", "\n", "pprinter = pp(indent=2)\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--model_path', type=str, required=True)\n", "parser.add_argument('--training_config', type=str, required=True)\n", "parser.add_argument('--cache_dir', type=str, required=True)\n", "parser.add_argument('--num_samples', type=int, default=1)\n", "parser.add_argument('--sft', action='store_true', required=False)\n", "parser.add_argument('--dpo', action='store_true', required=False)\n", "parser.add_argument('--rl', action='store_true', required=False)\n", "args = parser.parse_args()\n", "with open(args.training_config, 'r') as file:\n", "    training_config_dict = yaml.safe_load(file)\n", "\n", "print(args)\n", "pprinter.pprint(training_config_dict)\n", "assert args.sft or args.dpo or args.rl"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/IDSL_LLM_Docker/dataset_wrappers/alpaca.py:7: UserWarning: WARNING: Unsloth should be imported before transformers to ensure all optimizations are applied. Your code may run slower or encounter memory issues without these optimizations.\n", "\n", "Please restructure your imports with 'import unsloth' at the top of your file.\n", "  from unsloth.chat_templates import get_chat_template\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-11 14:50:56.890573: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.\n", "2025-04-11 14:50:56.898761: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:485] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "2025-04-11 14:50:56.907814: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:8454] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "2025-04-11 14:50:56.910566: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1452] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "2025-04-11 14:50:56.918192: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI AVX512_BF16 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2025-04-11 14:50:57.525636: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 04-11 14:51:00 [__init__.py:239] Automatically detected platform cuda.\n", "[2025-04-11 14:51:00,376] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/sygen_conda/compiler_compat/ld: cannot find -laio: No such file or directory\n", "collect2: error: ld returned 1 exit status\n", "/home/<USER>/anaconda3/envs/sygen_conda/compiler_compat/ld: cannot find -laio: No such file or directory\n", "collect2: error: ld returned 1 exit status\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading dataset: /data/server_cache/tatsu-lab--alpaca\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dd36bd1e64e14355a4838f1d7303e549", "version_major": 2, "version_minor": 0}, "text/plain": ["Standardizing format:   0%|          | 0/46801 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e87afa96cca74528bda77fb8b118f5a7", "version_major": 2, "version_minor": 0}, "text/plain": ["Standardizing format:   0%|          | 0/5201 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['instruction', 'input', 'output', 'text', 'conversations'],\n", "        num_rows: 46801\n", "    })\n", "    test: Dataset({\n", "        features: ['instruction', 'input', 'output', 'text', 'conversations'],\n", "        num_rows: 5201\n", "    })\n", "})\n", "Split name: train\n", "Samples:\n", "1: {'instruction': 'Give three tips for staying healthy.', 'input': '', 'output': '1.Eat a balanced diet and make sure to include plenty of fruits and vegetables. \\n2. Exercise regularly to keep your body active and strong. \\n3. Get enough sleep and maintain a consistent sleep schedule.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nGive three tips for staying healthy.\\n\\n### Response:\\n1.Eat a balanced diet and make sure to include plenty of fruits and vegetables. \\n2. Exercise regularly to keep your body active and strong. \\n3. Get enough sleep and maintain a consistent sleep schedule.'}\n", "2: {'instruction': 'What are the three primary colors?', 'input': '', 'output': 'The three primary colors are red, blue, and yellow.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nWhat are the three primary colors?\\n\\n### Response:\\nThe three primary colors are red, blue, and yellow.'}\n", "3: {'instruction': 'Describe the structure of an atom.', 'input': '', 'output': 'An atom is made up of a nucleus, which contains protons and neutrons, surrounded by electrons that travel in orbits around the nucleus. The protons and neutrons have a positive charge, while the electrons have a negative charge, resulting in an overall neutral atom. The number of each particle determines the atomic number and the type of atom.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nDescribe the structure of an atom.\\n\\n### Response:\\nAn atom is made up of a nucleus, which contains protons and neutrons, surrounded by electrons that travel in orbits around the nucleus. The protons and neutrons have a positive charge, while the electrons have a negative charge, resulting in an overall neutral atom. The number of each particle determines the atomic number and the type of atom.'}\n", "4: {'instruction': 'How can we reduce air pollution?', 'input': '', 'output': 'There are a number of ways to reduce air pollution, such as shifting to renewable energy sources, encouraging the use of public transportation, prohibiting the burning of fossil fuels, implementing policies to reduce emissions from industrial sources, and implementing vehicle emissions standards. Additionally, individuals can do their part to reduce air pollution by reducing car use, avoiding burning materials such as wood, and changing to energy efficient appliances.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nHow can we reduce air pollution?\\n\\n### Response:\\nThere are a number of ways to reduce air pollution, such as shifting to renewable energy sources, encouraging the use of public transportation, prohibiting the burning of fossil fuels, implementing policies to reduce emissions from industrial sources, and implementing vehicle emissions standards. Additionally, individuals can do their part to reduce air pollution by reducing car use, avoiding burning materials such as wood, and changing to energy efficient appliances.'}\n", "5: {'instruction': 'Describe a time when you had to make a difficult decision.', 'input': '', 'output': 'I had to make a difficult decision when I was working as a project manager at a construction company. I was in charge of a project that needed to be completed by a certain date in order to meet the client’s expectations. However, due to unexpected delays, we were not able to meet the deadline and so I had to make a difficult decision. I decided to extend the deadline, but I had to stretch the team’s resources even further and increase the budget. Although it was a risky decision, I ultimately decided to go ahead with it to ensure that the project was completed on time and that the client’s expectations were met. The project was eventually successfully completed and this was seen as a testament to my leadership and decision-making abilities.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nDescribe a time when you had to make a difficult decision.\\n\\n### Response:\\nI had to make a difficult decision when I was working as a project manager at a construction company. I was in charge of a project that needed to be completed by a certain date in order to meet the client’s expectations. However, due to unexpected delays, we were not able to meet the deadline and so I had to make a difficult decision. I decided to extend the deadline, but I had to stretch the team’s resources even further and increase the budget. Although it was a risky decision, I ultimately decided to go ahead with it to ensure that the project was completed on time and that the client’s expectations were met. The project was eventually successfully completed and this was seen as a testament to my leadership and decision-making abilities.'}\n", "\n"]}], "source": ["try:\n", "    dataset_args = training_config_dict['dataset_arguments']\n", "    dataset_dict = {}\n", "    if dataset_args['dataset_split'] is not None:\n", "        dataset_dict['split_name'] = dataset_args['dataset_split']\n", "    if dataset_args['dataset_subset'] is not None:\n", "        dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                cache_dir=dataset_args['cache_dir'], \n", "                                test_size=dataset_args['test_split'], \n", "                                **dataset_dict,\n", "                                )\n", "    for split_name in dataset_obj.iterable_dataset.keys():\n", "        print(\"Split name:\", split_name)\n", "        print(\"Samples:\")\n", "        for idx, example in enumerate(dataset_obj.iterable_dataset[split_name]):\n", "            print(idx+1, end=\": \")\n", "            print(example)\n", "            # pprinter.pprint(example)\n", "            if idx+1==args.num_samples:\n", "                break\n", "        print()\n", "except Exception as e:\n", "    print(f\"Dataset loading failed with error: {e}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Checking SFT dataset format...\n", "Loaded dataset:\n", "Loading dataset: /data/server_cache/tatsu-lab--alpaca\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "000966a05f80428fb481f288a62cdc21", "version_major": 2, "version_minor": 0}, "text/plain": ["Standardizing format:   0%|          | 0/46801 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7713880dfaf243e497b0455f57a885ca", "version_major": 2, "version_minor": 0}, "text/plain": ["Standardizing format:   0%|          | 0/5201 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['instruction', 'input', 'output', 'text', 'conversations'],\n", "        num_rows: 46801\n", "    })\n", "    test: Dataset({\n", "        features: ['instruction', 'input', 'output', 'text', 'conversations'],\n", "        num_rows: 5201\n", "    })\n", "})\n", "Split name: train\n", "Samples:\n", "1: {'instruction': 'Compare and contrast data science and artificial intelligence.', 'input': '', 'output': 'Data science is the process of collecting, analyzing and interpreting data to discover insights and patterns, while artificial intelligence is the use of technologies to develop computer systems that can perform tasks that typically require human intelligence. Both disciplines are focused on using technology to provide meaningful insights and solutions. Data science can provide the foundation for machine learning, which is used in artificial intelligence algorithms. Both fields overlap in areas such as predictive analytics, NLP, computer vision, and robotics.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nCompare and contrast data science and artificial intelligence.\\n\\n### Response:\\nData science is the process of collecting, analyzing and interpreting data to discover insights and patterns, while artificial intelligence is the use of technologies to develop computer systems that can perform tasks that typically require human intelligence. Both disciplines are focused on using technology to provide meaningful insights and solutions. Data science can provide the foundation for machine learning, which is used in artificial intelligence algorithms. Both fields overlap in areas such as predictive analytics, NLP, computer vision, and robotics.', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nCompare and contrast data science and artificial intelligence.\\n\\n', 'role': 'user'}, {'content': '\\nData science is the process of collecting, analyzing and interpreting data to discover insights and patterns, while artificial intelligence is the use of technologies to develop computer systems that can perform tasks that typically require human intelligence. Both disciplines are focused on using technology to provide meaningful insights and solutions. Data science can provide the foundation for machine learning, which is used in artificial intelligence algorithms. Both fields overlap in areas such as predictive analytics, NLP, computer vision, and robotics.', 'role': 'assistant'}]}\n", "2: {'instruction': 'Provide a list of popular superheroes.', 'input': '', 'output': 'Some of the most popular superheroes include <PERSON>, <PERSON>, <PERSON> Flash, <PERSON> Woman, <PERSON><PERSON>, <PERSON> Panther, <PERSON> Man, Captain America, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nProvide a list of popular superheroes.\\n\\n### Response:\\nSome of the most popular superheroes include <PERSON>, <PERSON>, <PERSON> Flash, <PERSON> Woman, <PERSON><PERSON>, <PERSON> Panther, <PERSON> Man, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and A<PERSON><PERSON>.', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nProvide a list of popular superheroes.\\n\\n', 'role': 'user'}, {'content': '\\nSome of the most popular superheroes include <PERSON>, <PERSON>, <PERSON> Flash, <PERSON> Woman, <PERSON><PERSON>, <PERSON> Panther, <PERSON> Man, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.', 'role': 'assistant'}]}\n", "3: {'instruction': 'Determine the relationship between the following variables.', 'input': 'Age and intelligence', 'output': 'Generally, intelligence increases as age increases up to mid- to late adulthood before leveling off. Various studies have also suggested that intelligence can peak between the ages of 20 and 40 before declining again.', 'text': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nDetermine the relationship between the following variables.\\n\\n### Input:\\nAge and intelligence\\n\\n### Response:\\nGenerally, intelligence increases as age increases up to mid- to late adulthood before leveling off. Various studies have also suggested that intelligence can peak between the ages of 20 and 40 before declining again.', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nDetermine the relationship between the following variables.\\n\\n### Input:\\nAge and intelligence\\n\\n', 'role': 'user'}, {'content': '\\nGenerally, intelligence increases as age increases up to mid- to late adulthood before leveling off. Various studies have also suggested that intelligence can peak between the ages of 20 and 40 before declining again.', 'role': 'assistant'}]}\n", "4: {'instruction': 'Convert the following number from Roman numerals to Arabic numerals.', 'input': 'XIV', 'output': '14', 'text': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nConvert the following number from Roman numerals to Arabic numerals.\\n\\n### Input:\\nXIV\\n\\n### Response:\\n14', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nConvert the following number from Roman numerals to Arabic numerals.\\n\\n### Input:\\nXIV\\n\\n', 'role': 'user'}, {'content': '\\n14', 'role': 'assistant'}]}\n", "5: {'instruction': 'Identify the type of figurative language used in the phrase.', 'input': '\"The heat built up like a pressure cooker\"', 'output': 'The phrase uses a metaphor to compare the heat to a pressure cooker.', 'text': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nIdentify the type of figurative language used in the phrase.\\n\\n### Input:\\n\"The heat built up like a pressure cooker\"\\n\\n### Response:\\nThe phrase uses a metaphor to compare the heat to a pressure cooker.', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nIdentify the type of figurative language used in the phrase.\\n\\n### Input:\\n\"The heat built up like a pressure cooker\"\\n\\n', 'role': 'user'}, {'content': '\\nThe phrase uses a metaphor to compare the heat to a pressure cooker.', 'role': 'assistant'}]}\n", "\n", "Split name: test\n", "Samples:\n", "1: {'instruction': 'What event occurred in the US in 1910?', 'input': '', 'output': 'In 1910, the Boy Scouts of America was founded, the first US stamp with a black image was issued, and the first airborne passenger flight took place. The year also saw the Great Fire of 1910.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nWhat event occurred in the US in 1910?\\n\\n### Response:\\nIn 1910, the Boy Scouts of America was founded, the first US stamp with a black image was issued, and the first airborne passenger flight took place. The year also saw the Great Fire of 1910.', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nWhat event occurred in the US in 1910?\\n\\n', 'role': 'user'}, {'content': '\\nIn 1910, the Boy Scouts of America was founded, the first US stamp with a black image was issued, and the first airborne passenger flight took place. The year also saw the Great Fire of 1910.', 'role': 'assistant'}]}\n", "2: {'instruction': 'Cut the article down to 10 sentences.', 'input': 'This is an article about dog food. It talks about the different ingredients and benefits that various types of dog food have. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. It’s also important to understand the nutritional value of the food and how it will affect your dog’s health. There are many different types of dog food available, such as wet food, dry food, grain-free, freeze-dried and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized.', 'output': 'This is an article about dog food. It discusses the different ingredients, benefits, and nutritional values of different types of dog food. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. There are many types of dog food available, such as wet food, dry food, grain-free, freeze-dried, and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized. Wet food has more moisture, whereas dry food has more fiber. Grain-free food is easier to digest and has less fillers. Freeze-dried food is the closest to a raw diet, preserving the most natural vitamins and minerals. Raw food is the most primal form of nutrition for our furry friends.', 'text': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nCut the article down to 10 sentences.\\n\\n### Input:\\nThis is an article about dog food. It talks about the different ingredients and benefits that various types of dog food have. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. It’s also important to understand the nutritional value of the food and how it will affect your dog’s health. There are many different types of dog food available, such as wet food, dry food, grain-free, freeze-dried and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized.\\n\\n### Response:\\nThis is an article about dog food. It discusses the different ingredients, benefits, and nutritional values of different types of dog food. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. There are many types of dog food available, such as wet food, dry food, grain-free, freeze-dried, and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized. Wet food has more moisture, whereas dry food has more fiber. Grain-free food is easier to digest and has less fillers. Freeze-dried food is the closest to a raw diet, preserving the most natural vitamins and minerals. Raw food is the most primal form of nutrition for our furry friends.', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nCut the article down to 10 sentences.\\n\\n### Input:\\nThis is an article about dog food. It talks about the different ingredients and benefits that various types of dog food have. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. It’s also important to understand the nutritional value of the food and how it will affect your dog’s health. There are many different types of dog food available, such as wet food, dry food, grain-free, freeze-dried and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized.\\n\\n', 'role': 'user'}, {'content': '\\nThis is an article about dog food. It discusses the different ingredients, benefits, and nutritional values of different types of dog food. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. There are many types of dog food available, such as wet food, dry food, grain-free, freeze-dried, and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized. Wet food has more moisture, whereas dry food has more fiber. Grain-free food is easier to digest and has less fillers. Freeze-dried food is the closest to a raw diet, preserving the most natural vitamins and minerals. Raw food is the most primal form of nutrition for our furry friends.', 'role': 'assistant'}]}\n", "3: {'instruction': 'Make a list of 10 activities that can be done while social distancing.', 'input': '', 'output': '1. Virtual hangouts with friends. \\n2. Playing online games.\\n3. Watching movies and TV shows.\\n4. Reading books.\\n5. Doing puzzles and other brain teasers.\\n6. Doing arts and crafts.\\n7. Cooking and baking.\\n8. Exercising at home.\\n9. Doing household projects.\\n10. Going for walks or bike rides outdoors.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nMake a list of 10 activities that can be done while social distancing.\\n\\n### Response:\\n1. Virtual hangouts with friends. \\n2. Playing online games.\\n3. Watching movies and TV shows.\\n4. Reading books.\\n5. Doing puzzles and other brain teasers.\\n6. Doing arts and crafts.\\n7. Cooking and baking.\\n8. Exercising at home.\\n9. Doing household projects.\\n10. Going for walks or bike rides outdoors.', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nMake a list of 10 activities that can be done while social distancing.\\n\\n', 'role': 'user'}, {'content': '\\n1. Virtual hangouts with friends. \\n2. Playing online games.\\n3. Watching movies and TV shows.\\n4. Reading books.\\n5. Doing puzzles and other brain teasers.\\n6. Doing arts and crafts.\\n7. Cooking and baking.\\n8. Exercising at home.\\n9. Doing household projects.\\n10. Going for walks or bike rides outdoors.', 'role': 'assistant'}]}\n", "4: {'instruction': 'Describe a quantum computing algorithm', 'input': '', 'output': \"One quantum computing algorithm that has been used in recent years is <PERSON><PERSON>'s algorithm. It is a quantum algorithm used to speed up database searches, especially when the database is too large to search using classical computing. The algorithm uses quantum entanglement to reduce the number of operations needed to search through the database.\", 'text': \"Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nDescribe a quantum computing algorithm\\n\\n### Response:\\nOne quantum computing algorithm that has been used in recent years is <PERSON><PERSON>'s algorithm. It is a quantum algorithm used to speed up database searches, especially when the database is too large to search using classical computing. The algorithm uses quantum entanglement to reduce the number of operations needed to search through the database.\", 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nDescribe a quantum computing algorithm\\n\\n', 'role': 'user'}, {'content': \"\\nOne quantum computing algorithm that has been used in recent years is <PERSON><PERSON>'s algorithm. It is a quantum algorithm used to speed up database searches, especially when the database is too large to search using classical computing. The algorithm uses quantum entanglement to reduce the number of operations needed to search through the database.\", 'role': 'assistant'}]}\n", "5: {'instruction': 'Make a list of 3 different ways to reduce carbon emissions.', 'input': '', 'output': '1. Energy conservation: use less energy whenever possible and switch to renewable energy sources.\\n2. Transportation: Use more public transportation, carpool, and buy hybrid or electric vehicles.\\n3. Reduce Waste: Reuse and recycle items, compost, and reduce consumption of resources.', 'text': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nMake a list of 3 different ways to reduce carbon emissions.\\n\\n### Response:\\n1. Energy conservation: use less energy whenever possible and switch to renewable energy sources.\\n2. Transportation: Use more public transportation, carpool, and buy hybrid or electric vehicles.\\n3. Reduce Waste: Reuse and recycle items, compost, and reduce consumption of resources.', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nMake a list of 3 different ways to reduce carbon emissions.\\n\\n', 'role': 'user'}, {'content': '\\n1. Energy conservation: use less energy whenever possible and switch to renewable energy sources.\\n2. Transportation: Use more public transportation, carpool, and buy hybrid or electric vehicles.\\n3. Reduce Waste: Reuse and recycle items, compost, and reduce consumption of resources.', 'role': 'assistant'}]}\n", "\n", "Processed dataset:\n", "Model does not have a padding token! Will use pad_token = <|finetune_right_pad_id|>.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "08b08cadd297454eb48ec7b7322e1588", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/46801 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a10b9d72093e4a3b93ce19b4bac64dc9", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/5201 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['text', 'conversations'],\n", "        num_rows: 46801\n", "    })\n", "    test: Dataset({\n", "        features: ['text', 'conversations'],\n", "        num_rows: 5201\n", "    })\n", "})\n", "Split name: train\n", "Samples:\n", "1: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nCompare and contrast data science and artificial intelligence.\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nData science is the process of collecting, analyzing and interpreting data to discover insights and patterns, while artificial intelligence is the use of technologies to develop computer systems that can perform tasks that typically require human intelligence. Both disciplines are focused on using technology to provide meaningful insights and solutions. Data science can provide the foundation for machine learning, which is used in artificial intelligence algorithms. Both fields overlap in areas such as predictive analytics, NLP, computer vision, and robotics.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nCompare and contrast data science and artificial intelligence.\\n\\n', 'role': 'user'}, {'content': '\\nData science is the process of collecting, analyzing and interpreting data to discover insights and patterns, while artificial intelligence is the use of technologies to develop computer systems that can perform tasks that typically require human intelligence. Both disciplines are focused on using technology to provide meaningful insights and solutions. Data science can provide the foundation for machine learning, which is used in artificial intelligence algorithms. Both fields overlap in areas such as predictive analytics, NLP, computer vision, and robotics.', 'role': 'assistant'}]}\n", "2: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nProvide a list of popular superheroes.\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nSome of the most popular superheroes include <PERSON>, <PERSON>, <PERSON> Flash, <PERSON> Woman, <PERSON><PERSON>, <PERSON> Panther, <PERSON> Man, Captain <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and A<PERSON><PERSON>.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nProvide a list of popular superheroes.\\n\\n', 'role': 'user'}, {'content': '\\nSome of the most popular superheroes include <PERSON>, <PERSON>, <PERSON> Flash, <PERSON> Woman, <PERSON><PERSON>, <PERSON> Panther, <PERSON> Man, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.', 'role': 'assistant'}]}\n", "3: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nDetermine the relationship between the following variables.\\n\\n### Input:\\nAge and intelligence\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nGenerally, intelligence increases as age increases up to mid- to late adulthood before leveling off. Various studies have also suggested that intelligence can peak between the ages of 20 and 40 before declining again.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nDetermine the relationship between the following variables.\\n\\n### Input:\\nAge and intelligence\\n\\n', 'role': 'user'}, {'content': '\\nGenerally, intelligence increases as age increases up to mid- to late adulthood before leveling off. Various studies have also suggested that intelligence can peak between the ages of 20 and 40 before declining again.', 'role': 'assistant'}]}\n", "4: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nConvert the following number from Roman numerals to Arabic numerals.\\n\\n### Input:\\nXIV\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\n14<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nConvert the following number from Roman numerals to Arabic numerals.\\n\\n### Input:\\nXIV\\n\\n', 'role': 'user'}, {'content': '\\n14', 'role': 'assistant'}]}\n", "5: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nIdentify the type of figurative language used in the phrase.\\n\\n### Input:\\n\"The heat built up like a pressure cooker\"\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nThe phrase uses a metaphor to compare the heat to a pressure cooker.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nIdentify the type of figurative language used in the phrase.\\n\\n### Input:\\n\"The heat built up like a pressure cooker\"\\n\\n', 'role': 'user'}, {'content': '\\nThe phrase uses a metaphor to compare the heat to a pressure cooker.', 'role': 'assistant'}]}\n", "\n", "Split name: test\n", "Samples:\n", "1: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nWhat event occurred in the US in 1910?\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nIn 1910, the Boy Scouts of America was founded, the first US stamp with a black image was issued, and the first airborne passenger flight took place. The year also saw the Great Fire of 1910.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nWhat event occurred in the US in 1910?\\n\\n', 'role': 'user'}, {'content': '\\nIn 1910, the Boy Scouts of America was founded, the first US stamp with a black image was issued, and the first airborne passenger flight took place. The year also saw the Great Fire of 1910.', 'role': 'assistant'}]}\n", "2: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nCut the article down to 10 sentences.\\n\\n### Input:\\nThis is an article about dog food. It talks about the different ingredients and benefits that various types of dog food have. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. It’s also important to understand the nutritional value of the food and how it will affect your dog’s health. There are many different types of dog food available, such as wet food, dry food, grain-free, freeze-dried and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized.\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nThis is an article about dog food. It discusses the different ingredients, benefits, and nutritional values of different types of dog food. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. There are many types of dog food available, such as wet food, dry food, grain-free, freeze-dried, and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized. Wet food has more moisture, whereas dry food has more fiber. Grain-free food is easier to digest and has less fillers. Freeze-dried food is the closest to a raw diet, preserving the most natural vitamins and minerals. Raw food is the most primal form of nutrition for our furry friends.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nCut the article down to 10 sentences.\\n\\n### Input:\\nThis is an article about dog food. It talks about the different ingredients and benefits that various types of dog food have. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. It’s also important to understand the nutritional value of the food and how it will affect your dog’s health. There are many different types of dog food available, such as wet food, dry food, grain-free, freeze-dried and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized.\\n\\n', 'role': 'user'}, {'content': '\\nThis is an article about dog food. It discusses the different ingredients, benefits, and nutritional values of different types of dog food. Some dogs require more protein than others, so it’s important to know what type of food your pet needs. There are many types of dog food available, such as wet food, dry food, grain-free, freeze-dried, and raw dog food. Each type has its own unique benefits that can help your pup stay healthy and energized. Wet food has more moisture, whereas dry food has more fiber. Grain-free food is easier to digest and has less fillers. Freeze-dried food is the closest to a raw diet, preserving the most natural vitamins and minerals. Raw food is the most primal form of nutrition for our furry friends.', 'role': 'assistant'}]}\n", "3: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nMake a list of 10 activities that can be done while social distancing.\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\n1. Virtual hangouts with friends. \\n2. Playing online games.\\n3. Watching movies and TV shows.\\n4. Reading books.\\n5. Doing puzzles and other brain teasers.\\n6. Doing arts and crafts.\\n7. Cooking and baking.\\n8. Exercising at home.\\n9. Doing household projects.\\n10. Going for walks or bike rides outdoors.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nMake a list of 10 activities that can be done while social distancing.\\n\\n', 'role': 'user'}, {'content': '\\n1. Virtual hangouts with friends. \\n2. Playing online games.\\n3. Watching movies and TV shows.\\n4. Reading books.\\n5. Doing puzzles and other brain teasers.\\n6. Doing arts and crafts.\\n7. Cooking and baking.\\n8. Exercising at home.\\n9. Doing household projects.\\n10. Going for walks or bike rides outdoors.', 'role': 'assistant'}]}\n", "4: {'text': \"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nDescribe a quantum computing algorithm\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\nOne quantum computing algorithm that has been used in recent years is <PERSON><PERSON>'s algorithm. It is a quantum algorithm used to speed up database searches, especially when the database is too large to search using classical computing. The algorithm uses quantum entanglement to reduce the number of operations needed to search through the database.<|eot_id|>\", 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nDescribe a quantum computing algorithm\\n\\n', 'role': 'user'}, {'content': \"\\nOne quantum computing algorithm that has been used in recent years is <PERSON><PERSON>'s algorithm. It is a quantum algorithm used to speed up database searches, especially when the database is too large to search using classical computing. The algorithm uses quantum entanglement to reduce the number of operations needed to search through the database.\", 'role': 'assistant'}]}\n", "5: {'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\nBelow is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n\\nMake a list of 3 different ways to reduce carbon emissions.\\n\\n<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\\n1. Energy conservation: use less energy whenever possible and switch to renewable energy sources.\\n2. Transportation: Use more public transportation, carpool, and buy hybrid or electric vehicles.\\n3. Reduce Waste: Reuse and recycle items, compost, and reduce consumption of resources.<|eot_id|>', 'conversations': [{'content': 'Below is an instruction that describes a task. Write a response that appropriately completes the request.\\n\\n', 'role': 'system'}, {'content': '\\nMake a list of 3 different ways to reduce carbon emissions.\\n\\n', 'role': 'user'}, {'content': '\\n1. Energy conservation: use less energy whenever possible and switch to renewable energy sources.\\n2. Transportation: Use more public transportation, carpool, and buy hybrid or electric vehicles.\\n3. Reduce Waste: Reuse and recycle items, compost, and reduce consumption of resources.', 'role': 'assistant'}]}\n", "\n", "Dataset wrapper is working correctly. If you are still facing issues:         \n", "1. Please verify the output samples for the loaded and processed datasets.         \n", "2. Please delete the dataset cache located at '/data/server_cache/tatsu-lab--alpaca'\n"]}], "source": ["try:\n", "    if args.sft:\n", "        print(\"Checking SFT dataset format...\")\n", "        print('Loaded dataset:')\n", "        dataset_args = training_config_dict['dataset_arguments']\n", "        dataset_dict = {}\n", "        if dataset_args['dataset_split'] is not None:\n", "            dataset_dict['split_name'] = dataset_args['dataset_split']\n", "        if dataset_args['dataset_subset'] is not None:\n", "            dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "        dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "        dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                    cache_dir=dataset_args['cache_dir'], \n", "                                    test_size=dataset_args['test_split'], \n", "                                    **dataset_dict,\n", "                                    )\n", "        for split_name in dataset_obj.iterable_dataset_traineval.keys():\n", "            print(\"Split name:\", split_name)\n", "            print(\"Samples:\")\n", "            for idx, example in enumerate(dataset_obj.iterable_dataset_traineval[split_name]):\n", "                print(idx+1, end=\": \")\n", "                print(example)\n", "                # pprinter.pprint(example)\n", "                if idx+1==args.num_samples:\n", "                    break\n", "            print()\n", "        \n", "        print('Processed dataset:')\n", "        assert type(dataset_obj).__mro__[-2] in [sft_dataset, grpo_dataset]\n", "        tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(args.model_path), chat_template=dataset_args['chat_template'])\n", "        print(dataset_obj.iterable_dataset_traineval)\n", "        for split_name in dataset_obj.iterable_dataset_traineval.keys():\n", "            print(\"Split name:\", split_name)\n", "            print(\"Samples:\")\n", "            for idx, example in enumerate(dataset_obj.iterable_dataset_traineval[split_name]):\n", "                print(idx+1, end=\": \")\n", "                print(example)\n", "                # pprinter.pprint(example)\n", "                if idx+1==args.num_samples:\n", "                    break\n", "            print()\n", "        assert set(dataset_obj.iterable_dataset_traineval['train'].column_names)==set([\"conversations\", \"text\"])\n", "        \n", "    if args.dpo:\n", "        print(\"Checking DPO dataset format...\")\n", "        print('Loaded dataset:')\n", "        dataset_args = training_config_dict['dataset_arguments']\n", "        dataset_dict = {}\n", "        if dataset_args['dataset_split'] is not None:\n", "            dataset_dict['split_name'] = dataset_args['dataset_split']\n", "        if dataset_args['dataset_subset'] is not None:\n", "            dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "        dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "        dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                    cache_dir=dataset_args['cache_dir'], \n", "                                    test_size=dataset_args['test_split'], \n", "                                    **dataset_dict,\n", "                                    )\n", "        for split_name in dataset_obj.iterable_dataset_traineval.keys():\n", "            print(\"Split name:\", split_name)\n", "            print(\"Samples:\")\n", "            for idx, example in enumerate(dataset_obj.iterable_dataset_traineval[split_name]):\n", "                print(idx+1, end=\": \")\n", "                print(example)\n", "                # pprinter.pprint(example)\n", "                if idx+1==args.num_samples:\n", "                    break\n", "            print()\n", "        \n", "        print('Processed dataset:')\n", "        assert type(dataset_obj).__mro__[-2] in [dpo_dataset, grpo_dataset]\n", "        if type(dataset_obj)==grpo_dataset:\n", "            tokenizer = dataset_obj.format_dataset(\n", "                tokenizer=AutoTokenizer.from_pretrained(args.model_path), chat_template=dataset_args['chat_template'], \n", "                ft_type='dpo',\n", "                chat_headers={\n", "                    \"system_header\":dataset_args['system_header'], \n", "                    \"user_header\":dataset_args['user_header'], \n", "                    \"assistant_header\":dataset_args['assistant_header']\n", "            })\n", "        else:\n", "            tokenizer = dataset_obj.format_dataset(\n", "                tokenizer=AutoTokenizer.from_pretrained(args.model_path), chat_template=dataset_args['chat_template'], \n", "                chat_headers={\n", "                    \"system_header\":dataset_args['system_header'], \n", "                    \"user_header\":dataset_args['user_header'], \n", "                    \"assistant_header\":dataset_args['assistant_header']\n", "            })\n", "            \n", "        print(dataset_obj.iterable_dataset_traineval)\n", "        for split_name in dataset_obj.iterable_dataset_traineval.keys():\n", "            print(\"Split name:\", split_name)\n", "            print(\"Samples:\")\n", "            for idx, example in enumerate(dataset_obj.iterable_dataset_traineval[split_name]):\n", "                print(idx+1, end=\": \")\n", "                print(example)\n", "                # pprinter.pprint(example)\n", "                if idx+1==args.num_samples:\n", "                    break\n", "            print()\n", "        assert set(dataset_obj.iterable_dataset_traineval['train'].column_names)==set([\"prompt\", \"chosen\", \"rejected\"])\n", "        \n", "    if args.rl:\n", "        print(\"Checking GRPO dataset format...\")\n", "        print('Loaded dataset:')\n", "        dataset_args = training_config_dict['dataset_arguments']\n", "        dataset_dict = {}\n", "        if dataset_args['dataset_split'] is not None:\n", "            dataset_dict['split_name'] = dataset_args['dataset_split']\n", "        if dataset_args['dataset_subset'] is not None:\n", "            dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "        dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "        dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                    cache_dir=dataset_args['cache_dir'], \n", "                                    test_size=dataset_args['test_split'], \n", "                                    **dataset_dict,\n", "                                    )\n", "        for split_name in dataset_obj.iterable_dataset_traineval.keys():\n", "            print(\"Split name:\", split_name)\n", "            print(\"Samples:\")\n", "            for idx, example in enumerate(dataset_obj.iterable_dataset_traineval[split_name]):\n", "                print(idx+1, end=\": \")\n", "                print(example)\n", "                # pprinter.pprint(example)\n", "                if idx+1==args.num_samples:\n", "                    break\n", "            print()\n", "        \n", "        print('Processed dataset:')\n", "        assert type(dataset_obj).__mro__[-2]==grpo_dataset\n", "        tokenizer = dataset_obj.format_dataset(\n", "            tokenizer=AutoTokenizer.from_pretrained(args.model_path), chat_template=dataset_args['chat_template'], \n", "            ft_type='rl',\n", "            chat_headers={\n", "                \"system_header\":dataset_args['system_header'], \n", "                \"user_header\":dataset_args['user_header'], \n", "                \"assistant_header\":dataset_args['assistant_header']\n", "        })\n", "        \n", "        print(dataset_obj.iterable_dataset_traineval)\n", "        for split_name in dataset_obj.iterable_dataset_traineval.keys():\n", "            print(\"Split name:\", split_name)\n", "            print(\"Samples:\")\n", "            for idx, example in enumerate(dataset_obj.iterable_dataset_traineval[split_name]):\n", "                print(idx+1, end=\": \")\n", "                print(example)\n", "                # pprinter.pprint(example)\n", "                if idx+1==args.num_samples:\n", "                    break\n", "            print()\n", "        assert set(dataset_obj.iterable_dataset_traineval['train'].column_names)==set([\"prompt\", \"thought\", \"answer\"])\n", "    print(\"Dataset wrapper is working correctly. If you are still facing issues: \\\n", "        \\n1. Please verify the output samples for the loaded and processed datasets. \\\n", "        \\n2. Please delete the dataset cache located at '{}'\".format(dataset_obj.cache_dir))\n", "except Exception as e:\n", "    print(f\"Dataset processing failed with error: {e}\")\n", "    print(\"\\n1. Please verify the output samples for the loaded and processed datasets. \\\n", "        \\n2. Please delete the dataset cache located at '{}'\".format(dataset_obj.cache_dir))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sygen_conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}