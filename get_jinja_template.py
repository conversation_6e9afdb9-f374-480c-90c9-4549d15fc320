# %%
import os, sys
import argparse, pprint
from os import listdir
from os.path import isfile, isdir, join
from argparse import Namespace

os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# %%
import torch
import torch.nn as nn
from transformers import pipeline, TextStreamer
from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig
from transformers import GPTQConfig, BitsAndBytesConfig
from tokenizers import tokenizers

# %%
# from unsloth import PatchDPOTrainer
# PatchDPOTrainer()
import huggingface_hub
import bitsandbytes
import accelerate
from huggingface_hub import hf_hub_download, snapshot_download
from peft import LoraConfig, PeftModel, get_peft_model
from peft.utils import _get_submodules
from bitsandbytes.functional import dequantize_4bit
from accelerate.state import PartialState, AcceleratorState

torch.cuda.set_device(0)
parser = argparse.ArgumentParser()
parser.add_argument('--MODEL', required=True, type=str, help='See model-card.json')
parser.add_argument('--CACHE', type=str, default='', help='See model-card.json')
parser.add_argument('--TOKEN', required=True, type=str, help='See model-card.json')
args = parser.parse_args()
# print(args)

huggingface_hub.login(token=args.TOKEN, add_to_git_credential=False)
if len(args.CACHE)>0:
    args.MODEL_PATH = join(args.CACHE, args.MODEL)
else:
    args.MODEL_PATH = args.MODEL

if os.environ.get('QUANT_SOURCE_MODEL', "")!=args.MODEL and os.environ.get('QUANT_SOURCE_MODEL', "")!="":
    print('Trying to load tokenizer form:', os.environ['QUANT_SOURCE_MODEL'])
    tokenizer = AutoTokenizer.from_pretrained(os.environ['QUANT_SOURCE_MODEL'])
elif 'gguf' in args.MODEL.lower():
    # tokenizer = AutoTokenizer.from_pretrained('/'.join(args.MODEL_PATH.split('/')[:-1]))
    try:
        print('Trying to load tokenizer form local:', args.MODEL)
        tokenizer = AutoTokenizer.from_pretrained(args.MODEL)
    except Exception as e:
        print('Trying to load a tokenizer from:', args.MODEL.replace('-GGUF', '').replace('-gguf', ''))
        tokenizer = AutoTokenizer.from_pretrained(args.MODEL.replace('-GGUF', '').replace('-gguf', ''))
else:
    tokenizer = AutoTokenizer.from_pretrained(args.MODEL)

try:
    assert tokenizer
    print('Tokenizer loaded successfully.')
except Exception as e:
    print('A valid tokenizer could not be found. Please provide the TOKENIZER HF REPO NAME as the last argument.')
tokenizer.save_pretrained(args.MODEL_PATH)
chat_template = tokenizer.chat_template
with open(join(args.MODEL_PATH, 'jinja_template.txt'), 'w') as fp:
    fp.write(chat_template)
print('USING JINJA TEMPLATE:\n{}'.format(chat_template))
