{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "from os import listdir\n", "from os.path import isfile, isdir, join\n", "from tqdm.notebook import tqdm\n", "os.environ['LD_LIBRARY_PATH'] = '/usr/lib/x86_64-linux-gnu'\n", "os.environ['TOKENIZERS_PARALLELISM'] = 'false'\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import pprint\n", "import torch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig\n", "from transformers import BitsAndBytesConfig\n", "from accelerate.utils import load_and_quantize_model\n", "from peft import PeftConfig, PeftModel, get_peft_model\n", "from unsloth import FastLanguageModel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.argv = [\n", "    'script.py',\n", "    '--adapter_model', '/data/server_cache/meta-llama/Llama-3.2-3B-Instruct_adapter',\n", "    '--adapter_name', 'adapter',\n", "    '--cache_dir', '/data/server_cache',\n", "    '--quant_bits', '4',\n", "    '--gguf', 'q4_k_m',\n", "]\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--adapter_model', type=str, required=True)\n", "parser.add_argument('--cache_dir', type=str, required=True)\n", "parser.add_argument('--base_model', type=str, required=False, default=None)\n", "parser.add_argument('--adapter_name', type=str, required=False, default=\"adapter\")\n", "parser.add_argument('--quant_bits', type=int, required=False, default=0, choices=[0, 4, 8])\n", "parser.add_argument('--gguf', type=str, required=False, default=None)\n", "args = parser.parse_args()\n", "\n", "pp = pprint.PrettyPrinter(indent=2)\n", "pp.pprint(args)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.quant_bits==4:\n", "    bnb_config_dict = {\n", "        'bnb_load_in_4bit': True,\n", "        'bnb_4bit_use_double_quant': True,\n", "        'bnb_4bit_quant_type': 'nf4',\n", "        'bnb_4bit_compute_dtype': torch.bfloat16,\n", "    }\n", "    quantization_config = BitsAndBytesConfig(load_in_4bit = bnb_config_dict['bnb_load_in_4bit'], \n", "                                            bnb_4bit_use_double_quant = bnb_config_dict['bnb_4bit_use_double_quant'],\n", "                                            bnb_4bit_quant_type = bnb_config_dict['bnb_4bit_quant_type'],\n", "                                            bnb_4bit_compute_dtype = bnb_config_dict['bnb_4bit_compute_dtype'],\n", "                                            )\n", "elif args.quant_bits==8:\n", "    quantization_config = BitsAndBytesConfig(load_in_8bit=True)\n", "else:\n", "    quantization_config = None\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(args.adapter_model)\n", "adapter_config = PeftConfig.from_pretrained(args.adapter_model)\n", "if args.base_model is None:\n", "    args.base_model = adapter_config.base_model_name_or_path\n", "model_config = AutoConfig.from_pretrained(args.base_model).__dict__\n", "pp.pprint(adapter_config)\n", "pp.pprint(model_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'quantization_config' not in model_config.keys():\n", "    model = AutoModelForCausalLM.from_pretrained(\n", "        args.base_model,\n", "        device_map='auto',\n", "        cache_dir=args.cache_dir, \n", "        trust_remote_code=True, revision='main',\n", "        attn_implementation=\"flash_attention_2\",\n", "        torch_dtype=torch.bfloat16,\n", "        quantization_config = quantization_config,\n", "    )\n", "else:\n", "    model = AutoModelForCausalLM.from_pretrained(\n", "        args.base_model,\n", "        device_map='auto',\n", "        cache_dir=args.cache_dir, \n", "        trust_remote_code=True, revision='main',\n", "        attn_implementation=\"flash_attention_2\",\n", "        torch_dtype=torch.bfloat16\n", "    )\n", "    assert model_config['quantization_config']['quant_method'] == 'bitsandbytes'\n", "    if args.quant_bits==0 or not model_config['quantization_config'][f'load_in_{args.quant_bits}bit']:\n", "        model.dequantize()\n", "        if quantization_config is not None:\n", "            model = load_and_quantize_model(model, bnb_quantization_config=quantization_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = PeftModel.from_pretrained(model, args.adapter_model)\n", "model = model.merge_and_unload()\n", "model.save_pretrained(join(args.adapter_model, 'merged'), safe_serialization=True)\n", "tokenizer.save_pretrained(join(args.adapter_model, 'merged'))\n", "del(model)\n", "\n", "model_dir = join(args.adapter_model, 'merged')\n", "model, tokenizer = FastLanguageModel.from_pretrained(args.adapter_model)\n", "model.save_pretrained_merged(model_dir+'_lora_16bit', tokenizer, save_method = \"merged_16bit\")\n", "del(model)\n", "\n", "if args.gguf is not None:\n", "    base_path = model_dir+f'_gguf_{args.gguf}'\n", "    model, tokenizer = FastLanguageModel.from_pretrained(args.adapter_model)\n", "    model.save_pretrained_gguf(base_path, tokenizer, quantization_method = args.gguf)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sygen_conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}