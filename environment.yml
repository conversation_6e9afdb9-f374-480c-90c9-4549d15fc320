name: idsl_llm
channels:
  - conda-forge
  - pytorch
  - nvidia
  - defaults
dependencies:
  - python=3.10
  - pip
  - numpy>=2.2.0
  - pandas>=2.3.0
  - matplotlib>=3.10.0
  - scipy>=1.15.0
  - scikit-learn>=1.3.0
  - jupyter>=1.1.0
  - notebook>=7.4.0
  - ipywidgets>=8.1.0
  - pyyaml>=6.0.2
  - tqdm>=4.67.0
  - psutil>=7.0.0
  - pip:
    # Core ML Libraries
    - torch>=2.7.0
    - transformers>=4.53.0
    - datasets>=3.6.0
    - accelerate>=1.8.0
    - peft>=0.15.0
    - trl>=0.19.0
    
    # Quantization and Optimization
    - bitsandbytes>=0.42.0
    - safetensors>=0.5.0
    
    # Tokenization and Text Processing
    - regex>=2024.11.0
    - tokenizers>=0.21.0
    - sentencepiece>=0.2.0
    
    # Development Tools
    - papermill>=2.4.0
    
    # HTTP and Async Support
    - aiohttp>=3.12.0
    - requests>=2.32.0
    - fsspec>=2025.3.0
    
    # HuggingFace Integration
    - huggingface-hub>=0.33.0
    
    # Data Handling
    - pyarrow>=20.0.0
    - dill>=0.3.8
    - xxhash>=3.5.0
    - multiprocess>=0.70.16
    
    # Optional packages (uncomment as needed)
    # - deepspeed>=0.10.0
    # - flash-attn>=2.0.0
    # - vllm
    # - triton
    # - xformers
