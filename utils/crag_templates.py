# Evaluator scoring template
evaluator_system_template = """
You are an assistant that evaluates a score from 0.00 to 1.00 for the relevance of Context to the given Prompt. It is imperative that the score is not below 0.00 or over 1.00.
You are also given a score that was calculated using another scoring model called "BASE", you can return the exact same score if you believe that this score is correct.
"""

evaluator_human_template = """
Prompt: {question}
Context: {context}
BASE model score: {eval}
Evaluate the relevance score of Context to the given Prompt from 0.00 to 1.00.
Return output in a $JSON_BLOB, as shown:\n\n\
```json\n\
{{\n\
  "action": "Relevance Score",\n\
  "score": "Final Score in range [0.00, 1.00]  (MUST BE OF FLOAT DATA TYPE)"\n\
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB of a single action and nothing else.
"""

# Creating the RAG search chat prompt template
query_system_template = """You are an assistant for question-answering tasks and can use a RAG tool or knowledge_base tool to search relevant information in a knowledge base. \
Formulate a natural language QUERY that can be used by the RAG tool to retrieve semantically relevant information from the knowledge base. \
The query should be detailed and enable the RAG tool or knowledge_base tool to provide an answer to the QUESTION being asked by the user. \
"""

query_human_template = """Here is the question: {question}. 
Please reword the question to a natural language QUERY for performing semantic search on knowledge base.
Return output in a $JSON_BLOB, as shown:\n\n\
```json\n\
{{\n\
  "action": "Rewording question",\n\
  "query": "Natural language QUERY"\n\
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB of a single QUERY and nothing else.
"""

# Creating the specificity score chat prompt template
specificity_system_template = """You are an assistant that evaluates the specificity of questions being asked by a user \
and can assign a "specificity score" on a scale from 0 to 5. 
The specificity score indicates how specific the question being asked is. The scores are defined as follows:

Specificity Score Framework
---------------------------

Score 0: Completely Ambiguous
The question lacks clarity and details.
It is impossible to understand what information is being sought.
Example: 
Question: "What is it?"
Answer: 
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": 0\n\
}}\n\
```\n\n\

Score 1: Very Vague
The question provides very little context or details.
It is difficult to determine what the question is about.
Example: 
Question: "What do you think about the situation?"
Answer: 
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": 1\n\
}}\n\
```\n\n\

Score 2: Somewhat Vague
The question has some context but still lacks specific details.
It requires significant assumptions to answer.
Example: 
Question: "What do you think about the recent news?"
Answer: 
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": 2\n\
}}\n\
```\n\n\

Score 3: Neutral
The question is clear but lacks specific details.
It provides enough context to understand the general topic.
Example: 
Question: "What do you think about the recent economic changes?"
Answer: 
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": 3\n\
}}\n\
```\n\n\

Score 4: Specific
The question is clear and includes specific details.
It is easy to understand what information is being sought.
Example: 
Question: "What do you think about the recent changes in the stock market?"
Answer: 
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": 4\n\
}}\n\
```\n\n\

Score 5: Very, Very Specific
The question is very detailed and clear.
It leaves no room for ambiguity.
Example: 
Question: "What do you think about the recent 10% decline in the S&P 500 index over the past week?"
Answer: 
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": 5\n\
}}\n\
```\n\n\
"""

specificity_human_template = """
Evaluate the specificity score for the following question.
Question: {question}
Return output in a $JSON_BLOB, as shown:\n\n\
```json\n\
{{\n\
  "action": "Specificity Score",\n\
  "score": "Final Score in range [0, 5] (MUST BE OF INT DATA TYPE)"\n\
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB of a single action and nothing else.
"""

answer_system_template = """Please provide an answer to the 'Question', using the 'Knowledge Base' provided. \
Ensure that the answer includes comprehensive explanations, relevant examples, and any necessary context to fully address the 'Question'. \
Cite specific information from the knowledge base and elaborate on key points if required.
"""

answer_human_template = """Knowledge Base: {context}
Question: {question}
"""

self_rag_templates = {
  "system": """You will receive an instruction, evidence, and output. 
Your task is to evaluate if the output is supported by the information provided in the evidence.
Use the following entailment scale to generate a score:
- Fully supported - All information in output is supported by the evidence, or extractions
from the evidence. This is only applicable when the output and part of the evidence are
almost identical.
- Partially supported - The output is supported by the evidence to some extent, but there
is major information in the output that is not discussed in the evidence. For example, if an
instruction asks about two concepts and the evidence only discusses either of them, it should
be considered a "Partially supported".
- No support - The output completely ignores evidence or is unrelated to the
evidence. This can also happen if the provided evidence is irrelevant to the instruction.
- Contradictory - The output contradicts the provided evidence.

Make sure to always use the provided evidence to judge whether the output is supported by it or not. 
Do not check whether the output follows the instruction, only evaluate whether the output is supported by the evidence.
""",
  "human": """
INSTRUCTION:\n{instruction_prompt}\n
EVIDENCE:\n{evidence_prompt}\n
OUTPUT:\n{output_prompt}\n

Evaluate if the output is supported by the information provided in the evidence.
Answer in a $JSON_BLOB, using the following schema:\n\n\
```json\n\
{{\n\
  "entailment": <one of ["Fully supported", "Partially supported", "No support", "Contradictory"]>,\n\
  "explanation": <reason for the selected entailment option>\n\
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB and nothing else.
""",
  'search_system_template': "You are an AI that helps users find relevant information on the web. \
Your task is to generate a query that is optimized for a Google search.",
  'search_human_template': "Given the following Knowledge Base: \n\n{evidence} \n\nAnd the Question: {question} \n\n\
The Answer: '{answer}' is {entailment} by the Knowledge Base. \n\
Provide a web search query to find new information from the web to be included in the Knowledge Base, \
to ensure that the answer is fully supported by the updated Knowledge Base. \
Respond only with the web search query and nothing else."
}