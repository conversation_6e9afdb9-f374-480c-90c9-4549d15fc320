claim_generation_template = {
    'system': """You are a large language model trained by OpenAI. Your task is to assist users with generating claims based on the provided evidence. \
A "claim" is a statement or assertion made within the evidence expressing a belief, opinion, or fact. \
Given evidence, please extract one "claim", its associated "claim topic" and "claim target". 

Note: The claim should not contain ambiguous references, such as "he," "she," or "it," and should use complete names. \
If there are multiple topics, provide the most dominant "claim topic". The "claim target" (one entity) is the specific individual, \
group, organization or concept that the statement or assertion within a text is directed towards or about which it is making a case. \
The "claim topic" should be a simple phrase representing the claim's central argument concept. If there is no claim, please leave it empty. \
Always generate a claim based on the given evidence. Do not generate the evidence yourself.
""",
    'user': """EVIDENCE:
---------
{evidence}

Respond with a single $JSON_BLOB matching the following schema:\n\n\
```json\n\
{{\n\
  "claim": <string; generated claim based on provided evidence>,
  "evidence": <string; a very brief summary of provided evidence>,
  "claim target": <string; target of the generated claim>,
  "claim topic": <string; topic of the generated claim>
}}\n\
```\n\n\
Reminder to always respond with a valid $JSON_BLOB of a single claim and nothing else.
"""
}

entity_generation_template = {
    'system': """You are a large language model trained by OpenAI. \
Your task is to assist users with generating "entity" information based on the provided claim or query. \
A "claim" or "query" is a statement or question made within a text expressing a belief, opinion, \
fact or asking a question. The "entity" is the specific individual, group, or organization that \
the statement or question within a text is directed towards or about which it is making a case. \
Given a claim or query, please extract its associated "entities". Note: Extract up to a maximum of {k_num} \
unique "entities" sorted in the order from most important to least important.
""",
    'user': """{claim_type}:
{claim}

Respond with a single $JSON_BLOB matching the following schema:\n\n\
```json\n\
{{\n\
  "entities": <list of strings; comma seperated claim entites associated with the provided claim. Eg: "OpenAI, Microsoft, Sam Altman, Satya Nadella">
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB of entities and nothing else.
"""
}

# Creating the RAG search chat prompt template
query_system_template = """You are an assistant for question-answering tasks and can use a serach tool to find relevant information from a knowledge base. \
Formulate a natural language "query" that can be used by the search tool to retrieve semantically similar information from the knowledge base. \
The "query" should be detailed and enable the search tool to provide an answer to the question being asked by the user. \
"""

query_human_template = """Question: {question} \n\n\
Please reword the question to a natural language "query" for performing semantic search on knowledge base. \n\

Respond with a single $JSON_BLOB matching the following schema:\n\n\
```json\n\
{{\n\
  "query": <string; natural language query for searching semantically similar information>\n\
}}\n\
```\n\n\
Reminder to always respond with a valid $JSON_BLOB of a single query and nothing else.
"""

answer_system_template = """Please provide an answer to the 'Question', using the 'Knowledge Base' provided. \
Ensure that the answer includes comprehensive explanations, relevant examples, and any necessary context to fully address the 'Question'. \
Cite specific information from the knowledge base and elaborate on key points if required.
"""

answer_human_template = """Knowledge Base: {context}
Question: {question}
"""