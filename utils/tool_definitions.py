import os, sys
import copy
from os import listdir
from os.path import isfile, isdir, join

import pandas as pd
import numpy as np
import pickle as pkl
import json, ast
import sqlite3
import http.client
import tempfile, uuid
import pathlib, importlib
import asyncio, subprocess

from langchain import hub
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage
from langchain_core.output_parsers import Str<PERSON>utputParser, JsonOutputParser
from langchain_core.runnables import <PERSON>nableLambda
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader, TextLoader, CSVLoader, Docx2txtLoader, UnstructuredEPubLoader, BSHTMLLoader

from utils.crag_templates import answer_system_template, answer_human_template
from utils.tool_templates import *
from utils.sql_utils import sql_rag_response
from utils.repl_shell import PythonRE<PERSON>
from utils.web_scrapers import playwright_search, playwright_save_url
from utils.callbacks import IntermediateStepsCallback

from rag.cmrag import DocumentFile, get_rag, generate_rag_response, process_and_embed_documents

GLOBAL_LLM = None
CODING_LLM = None
UPDATE_RAG = False
GLOBAL_RETRIES = 5
CONVERSATION_BUFFER = []

TOOL_SQL_VECTORSTORE = None
TOOL_SQL_RETRIEVER = None
TOOL_SQL_DATABASE_NAME = None
TOOL_SQL_KNOWLEDGE_BASE = None

filesystem_root = join(f"{os.getenv('CACHE')}", "documents")
downloads_root = join(f"{os.getenv('CACHE')}", "downloads")
if not isdir(filesystem_root):
    os.mkdir(filesystem_root)
if not isdir(downloads_root):
    os.mkdir(downloads_root)

class DocumentLoader:
    supported_extensions = {
            ".pdf": PyPDFLoader,
            ".txt": TextLoader,
            ".csv": CSVLoader,
            ".epub": UnstructuredEPubLoader,
            ".docx": Docx2txtLoader,
            ".html": BSHTMLLoader,
        }

    @classmethod
    def load_document(cls, file_path: str) -> str:
        ext = pathlib.Path(file_path).suffix.lower()
        loader_class = cls.supported_extensions.get(ext)
        if not loader_class:
            raise ValueError(f"Unsupported file extension: {ext}")
        if not isfile(file_path):
            raise ValueError(f"File not found: {file_path}")
        loader = loader_class(file_path=file_path)
        return loader.load()

def set_sql_state(db_name, knowledge_base, vectorstore, retriever):
    global TOOL_SQL_DATABASE_NAME, TOOL_SQL_KNOWLEDGE_BASE
    global TOOL_SQL_VECTORSTORE, TOOL_SQL_RETRIEVER
    TOOL_SQL_DATABASE_NAME = db_name
    TOOL_SQL_KNOWLEDGE_BASE = knowledge_base
    TOOL_SQL_VECTORSTORE = vectorstore
    TOOL_SQL_RETRIEVER = retriever
    return db_name, knowledge_base, vectorstore, retriever

def remove_thinking_tokens(ai_message: AIMessage, start_though_token: Optional[str]='<think>', end_thought_token: Optional[str]='</think>'):
    start_index = ai_message.content.index(start_though_token)
    end_index = ai_message.content.index(end_thought_token) + len(end_thought_token)
    thought_message = ai_message.content[start_index:end_index]
    # callback_obj = IntermediateStepsCallback()
    # callback_obj.add_to_global_stream(thought_message)
    print(thought_message)
    ai_message.content = ai_message.content[end_index:]
    return ai_message

def set_llm_state(llm, coding_llm, has_thinking_tokens=False):
    global GLOBAL_LLM, CODING_LLM
    if has_thinking_tokens:
        GLOBAL_LLM = (llm | RunnableLambda(remove_thinking_tokens))
        CODING_LLM = (coding_llm | RunnableLambda(remove_thinking_tokens))
    else:
        GLOBAL_LLM = llm
        CODING_LLM = coding_llm
    return GLOBAL_LLM, CODING_LLM

def set_filesystem_root(dir_path):
    global filesystem_root
    if isdir(dir_path):
        filesystem_root = dir_path
    else:
        print(f"{dir_path} is not valid")
    return filesystem_root

def set_conversation_buffer(buffer):
    global CONVERSATION_BUFFER
    CONVERSATION_BUFFER = buffer
    return CONVERSATION_BUFFER

def verification_function(instruction_prompt, final_answer):
    global GLOBAL_LLM
    global CODING_LLM
    global CONVERSATION_BUFFER
    
    def make_previous_work(previous_work: List[str]) -> str:
        if previous_work is None or len(previous_work)==0:
            return ""
        else:
            previous_work = "\n\n".join(previous_work)
            return f"This is your previous work:\n\n{previous_work}"
    
    verification_prompt = ChatPromptTemplate.from_messages([
        ("system", VERIFICATION_TEMPLATE['system']),
        ("human", VERIFICATION_TEMPLATE['human']),
    ])
    verification_chain = (verification_prompt | GLOBAL_LLM | StrOutputParser())
    
    retry_ctr = 0
    response_dict = {'score': None, 'explanation': None}
    while retry_ctr<GLOBAL_RETRIES:
        try:
            llm_response = verification_chain.invoke({
                "instruction_prompt": instruction_prompt,
                "reasoning_steps": make_previous_work(CONVERSATION_BUFFER),
                "final_answer": final_answer,
            })
            response_dict = json_output_parser(CODING_LLM, llm_response, required_keys=['score', 'explanation'])
            break
        except Exception as e:
            print(f"Verification failed with error: {e}")
            retry_ctr += 1
            if retry_ctr==GLOBAL_RETRIES:
                response_dict = {'score':'None', 'explanation':'Answer could not be verified'}
    return response_dict

def json_output_parser(llm, message_content, required_keys=["action", "action_input"], force_parse:bool=False, max_retries=5):
    def clean_keys(keys_string):
        keys_string = keys_string.strip()
        if keys_string.startswith('[') and keys_string.endswith(']'):
            keys_string = keys_string[1:-1]
        keys_list = keys_string.split(',')
        return [f.strip() for f in keys_list if len(f.strip())>0] 
    
    retry_ctr = 0
    dict_str = message_content[message_content.find("{"):message_content.rfind("}")+1]
    
    while retry_ctr<max_retries:
        try:
            return ast.literal_eval(dict_str)
        except Exception as e:
            if force_parse:
                break
            if required_keys is None:
                json_parsing_prompt = ChatPromptTemplate.from_messages([
                    ("system", JSON_PARSER_TEMPLATE['keys']['json_parsing_system']),
                    ("human", JSON_PARSER_TEMPLATE['keys']['json_parsing_template']),
                ])
                json_debugger_chain = (json_parsing_prompt | llm | StrOutputParser())
                keys_string = json_debugger_chain.invoke({
                    "json_string": dict_str,
                })
                required_keys = clean_keys(keys_string)
            
            json_parsing_prompt = ChatPromptTemplate.from_messages([
                ("system", JSON_PARSER_TEMPLATE['value']['json_parsing_system']),
                ("human", JSON_PARSER_TEMPLATE['value']['json_parsing_template']),
            ])
            json_debugger_chain = (json_parsing_prompt | llm | StrOutputParser())
            parsed_dict = {}
            for k in required_keys:
                parsed_dict[k] = json_debugger_chain.invoke({
                "json_string": dict_str,
                "key_name": k,
            })
            dict_str = json.dumps(parsed_dict)
            retry_ctr += 1
    return json.loads(dict_str)

def fetch_top_rows_from_all_tables(database_name, n):
    """Iterate over all the tables in the specified SQLite database and return the top n rows from each table.

    Parameters:
        database_name (str): Name of the SQLite database file.
        n (int): Number of rows to fetch from each table.

    Returns:
        str: A formatted string representing each table and its top n rows.
    """
    result = []
    db_tables = {}
    try:
        # Connect to the SQLite database
        conn = sqlite3.connect(database_name)
        cursor = conn.cursor()

        # Fetch all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]

        # Iterate over each table and fetch top n rows
        for table in tables:
            query = f"SELECT * FROM {table} LIMIT {n}"
            df = pd.read_sql_query(query, conn)

            # Add table name and data to result
            result.append(f"Table: {table}\n")
            result.append(df.to_string(index=False))
            result.append("\n\n")
            db_tables[table] = df.columns.values.tolist()
    except sqlite3.Error as e:
        return None, f"An error occurred: {e}"
    finally:
        # Close the database connection
        if 'conn' in locals():
            conn.close()
    return db_tables, "\n".join(result)

async def get_google_api_results(query, result_page=0, num_results=5):
    api_key = os.environ['SERPER_DEV_KEY']
    conn = http.client.HTTPSConnection("google.serper.dev")
    headers = {
    'X-API-KEY': api_key,
    'Content-Type': 'application/json'
    }
    
    try:
        payload = json.dumps({
            "q": query,
            "num": num_results,
            "page":result_page,
        })
        conn.request("POST", "/search", payload, headers)
        response = conn.getresponse()    
    except Exception as e:
        print(f"Serper failed with error: {e}")
        return []
    
    results = []
    if response.status == 200:
        data = json.loads(response.read().decode("utf-8"))
        urls = []
        try:
            for l_idx in range(len(data['organic'])):
                if data['organic'][l_idx]["link"] is not None and data['organic'][l_idx]["title"] is not None:
                    urls.append(data['organic'][l_idx]["link"])
                    results.append({'link':urls[-1], 'title':data['organic'][l_idx]["title"]})
        except Exception as e:
            print(f"Google scraper failed with error: {e}")
        return results
    else:
        print(f"Request failed with status code: {response.status}")
        return results

async def repl_function(python_script: str, previous_work: Optional[str] = "") -> str:
    global GLOBAL_LLM
    global CODING_LLM
    global GLOBAL_RETRIES
    global filesystem_root
    outp = ""
    retry_ctr = 0
    python_repl = PythonREPL()
        
    try:
        assert GLOBAL_LLM is not None
        assert CODING_LLM is not None
        coding_prompt = ChatPromptTemplate.from_messages(
            [("human", repl_prompt)]
        )
        checking_prompt = ChatPromptTemplate.from_messages(
            [("human", checker_prompt)]
        )
        debugger_prompt = ChatPromptTemplate.from_messages(
            [("human", debugging_prompt)]
        )
        coding_chain = (coding_prompt | CODING_LLM | StrOutputParser())
        debugger_chain = (debugger_prompt | CODING_LLM | StrOutputParser())
        checker_chain = (checking_prompt | CODING_LLM | StrOutputParser())
        html_splitter = RecursiveCharacterTextSplitter(
            chunk_size=8192,
            chunk_overlap=256
        )
        
        def sanitize_code(code_script):
            code_script = code_script.replace('```python', '')
            code_script = code_script.replace('```', '')
            return code_script
        
        use_filesystem_root = False
        if python_script.split("\n\n")[-1].startswith("All referenced files and folders in the code should always have the prefix"):
            use_filesystem_root = True
        code_script = await coding_chain.ainvoke({'code_script': python_script})
        code_script = sanitize_code(code_script)
        print("REPL output:")
        
        while retry_ctr<GLOBAL_RETRIES:
            try:
                current_loop = asyncio.get_running_loop()
                repl_task = current_loop.run_in_executor(
                    None,
                    lambda:python_repl.run(code_script))
                outp = await repl_task
                outp = html_splitter.split_text(outp)
                outp = outp[0]
                
                print("\t", "Checking code execution status:")
                code_script_copy = copy.deepcopy(code_script)
                check_outp = await checker_chain.ainvoke({
                    'code_script': code_script_copy, 
                    'code_output': outp,
                })
                print(check_outp)
                
                check_outp = json_output_parser(CODING_LLM, check_outp, required_keys=["success", "description"])
                if check_outp['success'] == 'true':
                    break
                elif retry_ctr==(GLOBAL_RETRIES-1):
                    error_trace = f"{check_outp['description']}\n\n{outp}"
                    outp = f"The following python code script could not be executed: \n\n{code_script} \n\nIt produced the following error: \n\n{error_trace} \
\n\nKindly review and revise the provided Python code for the given task, ensuring any errors are identified and corrected."
                    break
                else:
                    print("\t", "Debugging code...")
                    error_trace = f"{check_outp['description']}\n\n{outp}"
                    code_script = await debugger_chain.ainvoke({
                        'code_script': code_script + \
                            (f"\n\nAll referenced files and folders in the code should always have the prefix '{filesystem_root}'" if use_filesystem_root else ""), 
                        'error_trace': error_trace,
                        'previous_work': previous_work,
                    })
                    code_script = sanitize_code(code_script)
                    retry_ctr += 1
            except Exception as e:
                print(f"Debugging REPL agent status: {e}")
                code_script = await debugger_chain.ainvoke({
                    'code_script': code_script, 
                    'error_trace': e,
                    'previous_work': previous_work,
                })
                code_script = sanitize_code(code_script)
                retry_ctr += 1
                outp = f"The provided python code script could not be executed and produced the following error: \n\n{e} \n\nPlease revise the python code."
                break
        return outp
    except Exception as e:
        return f"Script execution failed with error: {e}"

async def python_coding_function(code_specifications: Union[str, Dict[str, str]], sample_code: Optional[str] = "") -> str:
    global GLOBAL_LLM
    global CODING_LLM
    code_script = ""
    
    try:
        assert GLOBAL_LLM is not None
        assert CODING_LLM is not None
        prompt = ChatPromptTemplate.from_messages(
            [("human", python_code_prompt)]
        )
        llm_chain = (prompt | CODING_LLM | StrOutputParser())
        callback_obj = IntermediateStepsCallback()
        
        def make_previous_work(previous_work: List[str]) -> str:
            if len(previous_work)==0:
                return ""
            else:
                previous_work = "\n\n".join(previous_work)
                return f"This is your previous work:\n\n{previous_work}"
            
        code_script = await llm_chain.ainvoke({
            'user_specifications': str(code_specifications),
            'sample_code': sample_code if sample_code else "",
            'previous_work': make_previous_work(CONVERSATION_BUFFER)
        }, {"callbacks": [callback_obj]})
        
        return code_script
    except Exception as e:
        return f"Code script generation failed with error: {e}"

@tool(name_or_callable="database_docstring")
async def database_docstring(query: str):
    """AI assisted tool to generate comprehensive documentation for the tables in a connected SQL database based on a provided query. 
    This tool assists in understanding the database structure and contents, enabling more informed database information search and SQL query writing for data retrieval.

    Args:
        query (str): A detailed input specifying the requirements for the database documentation, such as the required table descriptions, column details (names, types, constraints) etc.

    Returns:
        str: A structured and detailed documentation of the database's tables.
    """
    global GLOBAL_LLM
    global TOOL_SQL_DATABASE_NAME, TOOL_SQL_RETRIEVER
    global TOOL_SQL_KNOWLEDGE_BASE
    map_reduce_prompt = ChatPromptTemplate.from_messages([("human", map_reduce_template)])
    map_reduce_chain = (map_reduce_prompt | GLOBAL_LLM | StrOutputParser())
    docstring_prompt = ChatPromptTemplate.from_messages(
        [("human", database_docstring_prompt)]
    )
    docstring_chain = (docstring_prompt | GLOBAL_LLM | StrOutputParser())
    
    database_structure = []
    try:
        callback_obj = IntermediateStepsCallback()
        database_tables, database_snapshot = fetch_top_rows_from_all_tables(TOOL_SQL_DATABASE_NAME, 5)
        for table_name in database_tables.keys():
            for column_name in database_tables[table_name]:
                query_prompt = database_rag_prompt.format(context=database_snapshot, query=f"Generate a detailed description for the column: {column_name} in the table: {table_name}.")
                print(query_prompt)
                response = await sql_rag_response(query_prompt, TOOL_SQL_RETRIEVER, GLOBAL_LLM, callback_manager=None)
                response = f"Description for column: {column_name} in table: {table_name}\n\n" + response
                database_structure.append(response)
                callback_obj.add_to_global_stream(response+"\n\n")
        
        # summary_list = []
        # for i in range(len(database_structure)):
        #     summary_response = await map_chain.ainvoke({"docs":database_structure[i]}, {"callbacks": [callback_obj]})
        #     summary_list.append(f"DOCUMENT SUMMARY {i}:\n" + summary_response)
        # summary_list = "\n\n".join(summary_list)
        
        TOOL_SQL_KNOWLEDGE_BASE = "\n\n".join(database_structure)
        summary_list = "\n\n".join(database_structure)
        TOOL_SQL_KNOWLEDGE_BASE = await map_reduce_chain.ainvoke({"docs":summary_list}, {"callbacks": [callback_obj]})
        callback_obj.add_to_global_stream("<END_OF_RAG>")
        
        response = await docstring_chain.ainvoke(input={
            'database_snapshot': database_snapshot,
            'knowledge_base': TOOL_SQL_KNOWLEDGE_BASE,
            'docstring_query': query, 
        })
        print(response)
        
        response = f"DATABASE SNIPPET: \n\n{database_snapshot} \n\n DATABASE DOCUMENTATION: \n\n{response} \n\n <END OF DOCUMENTATION>"
        TOOL_SQL_KNOWLEDGE_BASE = f"Database Snippet: \n\n{database_snapshot} \n\nDatabase Description: \n\n{TOOL_SQL_KNOWLEDGE_BASE}"
        return response
    except Exception as e:
        return f"Database docstring generation failed with exception: {e}"

@tool(name_or_callable="database_information")
async def database_information(query: str):
    """A tool for searching and retrieving information from a knowledge base that documents the structure and description of a connected SQL database.

    Args:
        query (str): A prompt specifying the information to search for within the database knowledge base.

    Returns:
        str: A response containing the requested database information.
    """
    global GLOBAL_LLM
    global TOOL_SQL_DATABASE_NAME, TOOL_SQL_RETRIEVER
    try:
        callback_obj = IntermediateStepsCallback()
        _, database_snapshot = fetch_top_rows_from_all_tables(TOOL_SQL_DATABASE_NAME, 5)
        query_prompt = database_rag_prompt.format(context=database_snapshot, query=query)
        response = await sql_rag_response(query_prompt, TOOL_SQL_RETRIEVER, GLOBAL_LLM, callback_manager=[callback_obj])
        callback_obj.add_to_global_stream("<END_OF_RAG>")
        return response
    except Exception as e:
        return f"Database information search failed with exception: {e}"

@tool(name_or_callable="database_retrieval")
async def database_retrieval(query: str):
    """
    A tool to retrieve specified data from a connected SQL database based on the provided query or task.

    Args:
        query (str): A comprehensive task description that provides clear and detailed instructions on what data to retrieve from the connected SQL database, \
        including specific tables, columns, filtering criteria, or any aggregation requirements. \
        Ensure the description specifies the format or structure in which the retrieved data should be presented, if applicable.

    Returns:
        str: A string representation of the retrieved data, such as a formatted table or serialized dataframe.
    """
    global CODING_LLM
    global CONVERSATION_BUFFER
    global TOOL_SQL_KNOWLEDGE_BASE
    
    def make_previous_work(previous_work: List[str]) -> str:
        if len(previous_work)==0:
            return ""
        else:
            previous_work = "\n\n".join(previous_work)
            return f"Refer to the following context to identify relevant information for error handling: \n\n{previous_work}"
    
    assert CODING_LLM is not None
    if TOOL_SQL_KNOWLEDGE_BASE is None:
        return """The SQL database documentation is not yet defined. \
Please ensure you first use the 'database_docstring' tool to document the structure and details of the connected database. \
Once the documentation is complete, proceed to use the 'database_retrieval' tool for data retrieval."""
    
    callback_obj = IntermediateStepsCallback()
    sql_specification_prompt = ChatPromptTemplate.from_messages([
        ("human", database_code_prompt)
    ])
    sql_specification_chain = (sql_specification_prompt | CODING_LLM | StrOutputParser())
    sql_specifications = await sql_specification_chain.ainvoke(
        {
            "knowledge_base": TOOL_SQL_KNOWLEDGE_BASE,
            "database_name": TOOL_SQL_DATABASE_NAME,
            "user_query": query,
        }, 
        {"callbacks": [callback_obj]}
    )
    
    retry_ctr = 0
    code_response = ""
    error_msg = ""
    outp = ""
    # database_tables, database_snapshot = fetch_top_rows_from_all_tables(TOOL_SQL_DATABASE_NAME, 5)
    # CONVERSATION_BUFFER.append(f"Database Snippet: \n\n{database_snapshot}")
    previous_work = make_previous_work(CONVERSATION_BUFFER)
    CONVERSATION_BUFFER.append("")
    
    while retry_ctr<GLOBAL_RETRIES:
        try:
            CONVERSATION_BUFFER[-1] = error_msg
            code_response = await python_coding_function(code_specifications=sql_specifications, 
                                                sample_code=sample_sql_code.format(database_name=TOOL_SQL_DATABASE_NAME))
            outp = await repl_function(code_response, previous_work)
            callback_obj.add_to_global_stream(outp)
            
            if outp.endswith("ensuring any errors are identified and corrected."):
                retry_ctr += 1
                error_msg = outp
            else:
                break
        except Exception as e:
            error_msg = f"\n\nData retrieval from SQL database failed with error: {e}. \n\
Please make the necessary changes in the following python code for a successful execution: \n\n{code_response}"
            outp = error_msg
            retry_ctr += 1
            if retry_ctr==GLOBAL_RETRIES:
                break
    return outp

@tool(name_or_callable="list_directory")
async def list_directory(dir_path: str) -> str:
    """Tool to list the directory structure relative to `dir_path` inside the current working directory.

    Args:
        dir_path (str): The directory path to start the traversal from inside the current working directory.

    Returns:
        str: The directory structure formatted as a string.
    """
    global filesystem_root
    dir_structure = ""
    try:
        def print_directory_structure(path, dir_structure, indent=''):
            for item in os.listdir(path):
                if item.startswith('.') or len(item.strip())==0:
                    continue
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    dir_structure += f"{indent}├── {os.path.basename(item_path)}\n"
                    dir_structure = print_directory_structure(item_path, dir_structure, indent + '│   ')
                else:
                    dir_structure += f"{indent}├── {item}\n"
            return dir_structure
        
        dir_structure = print_directory_structure(join(filesystem_root, dir_path), dir_structure)
        return dir_structure
    except Exception as e:
        return f"List directory structure failed with exception: {e}"

@tool(name_or_callable="save_file")
async def save_file(content: str, file_path: str) -> str:
    """Tool to save content as a text file at the specified `file_path` inside the current working directory.

    Args:
        content (str): Content to save to the text file.
        file_path (str): The file path relative to the current working directory.

    Returns:
        str: Message describing whether the file was saved successfully or not.
    """
    global filesystem_root
    try:
        if not isdir(filesystem_root):
            os.mkdir(filesystem_root)
        with open(join(filesystem_root, file_path), 'w') as fp:
            fp.write(content.encode('utf-8').decode('unicode-escape'))
        return f"File has been saved successfully as {file_path} in the current working directory."
    except Exception as e:
        return f"Saving file {file_path} failed with exception: {e}"
    
@tool(name_or_callable="read_file")
async def read_file(file_path: str) -> str:
    """Tool to read content from a text file at the specified `file_path` inside the current working directory.

    Args:
        file_path (str): The file path relative to the current working directory.

    Returns:
        str: Content of the text file.
    """
    global filesystem_root
    html_splitter = RecursiveCharacterTextSplitter(
        chunk_size=4096,
        chunk_overlap=256
    )
    
    try:
        # if not isfile(join(filesystem_root, file_path)):
        #     return f"{file_path} does not exist inside the current working directory."
        # with open(join(filesystem_root, file_path), 'r') as fp:
        #     content = fp.read()
        content = []
        print(f"Reading file {join(filesystem_root, file_path)}")
        document = DocumentLoader.load_document(join(filesystem_root, file_path))
        for page in document:
            content.append(page.page_content)
        content = html_splitter.split_text("\n\n".join(content))
        return content[0]
    except Exception as e:
        return f"Reading file {file_path} failed with exception: {e}"
    
@tool(name_or_callable="execute_program")
async def execute_program(program_path: str):
    """Tool to execute a bash(.sh) or python(.py) script program located in the current working directory and stream its standard output.

    Args:
        program_path (str): The relative path to the Bash or Python script located in the current working directory to be executed. This argument should not include any command-line instructions.

    Yields:
        str: Lines of output from the program.
    """
    global filesystem_root
    try:
        program_path = join(filesystem_root, program_path)
        if not isfile(program_path):
            raise ValueError("The input code file at 'program_path' does not exist. Make sure the file exists or provide a different 'program_path'.")
    except Exception as e:
        return f"Script execution failed with error: {e}"
    
    if program_path.split('.')[-1]=="sh":
        process = await asyncio.create_subprocess_exec(
            '/bin/bash',
            program_path,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
    elif program_path.split('.')[-1]=="py":
        process = await asyncio.create_subprocess_exec(
            sys.executable,
            program_path,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        
    try:
        stdout, stderr = await process.communicate()
        stdout = stdout.decode('utf-8') if stdout else ""
        stderr = stderr.decode('utf-8') if stderr else ""

        outputs = [f"Streaming standard output for {program_path}:"]
        outputs.append(stdout)
    except Exception as e:
        process.cancel()
        raise asyncio.CancelledError()
    finally:
        # await process.wait()
        if process.returncode != 0:
            outputs.append(stderr)
        else:
            outputs.append("Script program executed successfully.")
        return "\n".join(outputs)
        
@tool(name_or_callable="repl_tool")
async def repl_tool(python_script: str) -> str:
    """Tool to execute a Python code script and return its standard output. The script is executed in a Python REPL shell.

    Args:
        python_script (str): Python code script to execute.

    Returns:
        str: Standard output of the script execution from the shell console.
    """
    try:
        outp = await repl_function(python_script=python_script + \
            f"\n\nAll referenced files and folders in the code should always have the prefix '{filesystem_root}'")
        return outp
    except Exception as e:
        return f"Script execution failed with error: {e}"
    
@tool(name_or_callable="python_codepad")
async def python_codepad(code_specifications: Union[str, Dict[str, str]], sample_code: Optional[str] = "") -> str:
    """AI-assisted tool to generate a Python code script.

    Input to this tool should be a detailed `code_specifications` dictionary that outlines a set of requirements for the required code.
    
    If modifying an existing Python code, input the existing code as `sample_code` and specify the required changes in `code_specifications`.

    Args:
        code_specifications (Dict[str, str]): A dictionary containing details of the required Python code script:
            - component_name (str): A clear and descriptive name for the component.
            - purpose (str): A brief description of the purpose of the component. What problem or need does it solve?
            - functional_requirements (str): Detailed descriptions of the specific functionalities the component must provide.
            - input_output (str): Description of the inputs and outputs for the component.
            - validation_constraints (str, optional="None"): Input validation rules and constraints.
            - error_handling (str, optional="None"): How the component should handle errors or invalid inputs.
            - documentation (str, optional="None"): Documentation requirements for the component.
            - dependencies (str, optional="None"): Any external dependencies or services the component relies on.
            - integration_points (str, optional="None"): How the component integrates with other parts of the system.
        sample_code (str, optional="None"): The existing Python code to be modified, if applicable.

    Returns:
        str: Python code script.
    """
    try:
        code_specifications = str(code_specifications) +\
            f"\n\nAll referenced files and folders in the code should always have the prefix '{filesystem_root}'",
        code_script = await python_coding_function(code_specifications=code_specifications, sample_code=sample_code)
        return code_script
    except Exception as e:
        return f"Code script generation failed with error: {e}"
    
@tool(name_or_callable="direct_response")
async def direct_response(answer: str) -> str:
    """Tool to respond directly for any given task if no other provided tool is appropriate for solving the task.

    Args:
        answer (str): Response for the given task.

    Returns:
        str: Response as answer for the given task.
    """
    return answer

@tool(name_or_callable="knowledge_response")
async def knowledge_response(query: str) -> str:
    """Tool to respond to any given task with results based on your internal knowledge.

    Args:
        query (str): Prompt to search the required information for the given task from your internal knowledge.

    Returns:
        str: Response for the given task.
    """
    global GLOBAL_LLM
    assert GLOBAL_LLM is not None
    prompt = ChatPromptTemplate.from_messages(
        [("human", knowledge_prompt)]
    )
    llm_chain = (prompt | GLOBAL_LLM | StrOutputParser())
    search_result = await llm_chain.ainvoke({'text_prompt': query})
    return search_result

@tool(name_or_callable="web_scraper")
async def web_scraper(query: str) -> str:
    """AI assisted tool to scrape the internet to gather the necessary information required to complete the given task. \
    This tool saves the scraped results to the knowledge base for future reference and provides a summarized response for the input query.

    Args:
        query (str): Prompt to search the required information for the given task from the internet.

    Returns:
        str: Summarized response based on the scraped results for the input query.
    """
    global GLOBAL_LLM
    global UPDATE_RAG
    assert GLOBAL_LLM is not None
    search_prompt = ChatPromptTemplate.from_messages([
        ("system", scraper_prompt["search_system_template"]),
        ("human", scraper_prompt["search_human_template"]),
    ])
    answer_prompt = ChatPromptTemplate.from_messages([
        ("system", answer_system_template),
        ("human", answer_human_template),
    ])
    map_prompt = ChatPromptTemplate.from_messages([("human", map_template)])
    search_llm = (search_prompt | GLOBAL_LLM | StrOutputParser())
    answer_llm = (answer_prompt | GLOBAL_LLM | StrOutputParser())
    map_chain = (map_prompt | GLOBAL_LLM | StrOutputParser())
    
    search_query = await search_llm.ainvoke({
        "query": query,
    })
    print("Web Search:", search_query)
    
    results = []
    html_splitter = RecursiveCharacterTextSplitter(
        chunk_size=4096,
        chunk_overlap=256
    )
    
    try:
        results = await playwright_search(query, num_results=6, return_results=True)
    except Exception as e:
        results = []
    if len(results)==0:
        # results = []
        results = await get_google_api_results(query, 0, num_results=6)
    print(results)
        
    final_answer = ""
    links = [f['link'] for f in results]
    sanitized_titles = ["".join(c if c.isalnum() else "_" for c in f['title']) for f in results]
    file_names = [f"{join(downloads_root, f+'.html')}" for f in sanitized_titles]
    callback_obj = IntermediateStepsCallback()
    
    tasks = [asyncio.create_task(playwright_save_url(links[l_idx], f, return_filename=True)) for l_idx, f in enumerate(file_names)]
    rag_filenames = []
    values = await asyncio.gather(*tasks)
    html_content = []
    for i, f_name_and_val in enumerate(values):
        try:
            # html_content.append(await tasks[i])
            html_content.append(f_name_and_val[1])
            rag_filenames.append(f_name_and_val[0])
            print(f_name_and_val[0])
        except Exception as e:
            continue
    callback_obj.add_to_global_stream(f"Analysing retrieved links: \n\n" + "\n\n".join(links) + "\n\n")
    
    try:
        if UPDATE_RAG:
            file_paths = [DocumentFile(f) for f in rag_filenames if isfile(f)]
            try:
                current_loop = asyncio.get_running_loop()
                rag_task = current_loop.run_in_executor(None, lambda:get_rag(GLOBAL_LLM, callback_manager=[callback_obj]))
                vectorstore, _ = await rag_task
                # vectorstore, _ = await get_rag(GLOBAL_LLM, callback_manager=[callback_obj])
                
                current_task = process_and_embed_documents(file_paths, vectorstore)
                async for chunk in current_task:
                    callback_obj.add_to_global_stream(chunk)
            except Exception as e:
                print(f"RAG failed with error: {e}")
            finally:
                html_content = await generate_rag_response(query, vectorstore, [], GLOBAL_LLM, 
                                                        [callback_obj], None, use_standalone=False)
                callback_obj.add_to_global_stream("<END_OF_RAG>")
            final_answer = await answer_llm.ainvoke({
                "question": query,
                "context": html_content,
            })
        else:
            html_content = [html_splitter.split_text(f) for f in html_content if f!="HTTP 404"]
            # html_content = "\n\n".join([page[0] for page in html_content])
            map_answers = []
            for map_idx in range(len(html_content)):
                map_answer = await answer_llm.ainvoke({
                    "question": query,
                    "context": html_content[map_idx][0] if(len(html_content[map_idx])==1) else "\n\n".join(html_content[map_idx][:min(len(html_content[map_idx]), 6)]),
                })
                map_answers.append(map_answer)
            summary_list = "\n\n".join(map_answers)
            final_answer = await answer_llm.ainvoke({
                "question": query,
                "context": summary_list,
            })
    except Exception as e:
        print(f"Web scraper failed with error: {e}")
        final_answer = "Search results could not be retrieved. Please revise the search query and try again."
    
    links_str = "No links found."
    if len(links)>0:
        links_str = "\n\n".join(links)
    return f"Showing summarized search results from: \n\n{links_str} \n\n" + final_answer

@tool(name_or_callable="knowledge_search")
async def knowledge_search(query: str) -> str:
    """Tool to respond to any given task with search results based on your internal knowledge base. \
    The knowledge base was built using user-provided/attached/locally-stored documents. \
    The input to this tool should be a well-designed and detailed query that can be used to \
    search for required information in the knowledge base.

    Args:
        query (str): Prompt to search the required information for the given task from your internal knowledge base.

    Returns:
        str: Response for the given task.
    """
    global GLOBAL_LLM
    assert GLOBAL_LLM is not None
    
    answer_prompt = ChatPromptTemplate.from_messages([
        ("system", answer_system_template),
        ("human", answer_human_template),
    ])
    answer_llm = (answer_prompt | GLOBAL_LLM | StrOutputParser())
    
    rag_content = ""
    callback_obj = IntermediateStepsCallback()
    try:
        current_loop = asyncio.get_running_loop()
        rag_task = current_loop.run_in_executor(None, lambda:get_rag(GLOBAL_LLM, callback_manager=[callback_obj]))
        vectorstore, _ = await rag_task
        # vectorstore, _ = await get_rag(GLOBAL_LLM, callback_manager=[callback_obj])
        
        rag_content = await generate_rag_response(query, vectorstore, [], GLOBAL_LLM, 
                                                [callback_obj], None, use_standalone=False)
    except Exception as e:
        print(f"RAG failed with error: {e}")
        rag_content = f"No results found in knowledge base for the input query '{query}'. \n\n" + \
            "Use 'web_scraper' tool to search the relevant information from the web."
    finally:
        callback_obj.add_to_global_stream("<END_OF_RAG>")
    
    final_answer = await answer_llm.ainvoke({
        "question": query,
        "context": rag_content,
    })
    return "Results from the knowledge base:\n\n" + final_answer

@tool(name_or_callable="final_answer")
async def final_answer(answer: str) -> str:
    """A passthrough function for displaying the final answer to the user.

    Args:
        answer (str): Final answer generated by the AI Agent.

    Returns:
        str: Final answer to display to the user.
    """
    return answer

def get_available_tools():
    return list(tool_dict.keys())

def set_rag_update(update_rag):
    global UPDATE_RAG
    UPDATE_RAG = update_rag
    return UPDATE_RAG

tool_dict = {
    'list_directory': list_directory,
    'save_file': save_file,
    'read_file': read_file,
    'repl_tool': repl_tool,
    'execute_program': execute_program,
    'direct_response': direct_response,
    'knowledge_response': knowledge_response,
    'python_codepad': python_codepad,
    'web_scraper': web_scraper,
    'knowledge_search': knowledge_search,
    'database_docstring': database_docstring,
    'database_information': database_information,
    'database_retrieval': database_retrieval,
}
custom_stop = {
    'thought': ["Thought:", "THOUGHT:", "**Thought**", "# Thought"],
    'action': ["Action:", "ACTION:", "**Action**", "# Action"],
    'observation': ["Observation:", "OBSERVATION:", "**Observation**", "# Observation"], 
    'plan': ["<END_OF_PLAN>"],
}