from pydantic import BaseModel, Field
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Sequence, TypeVar, Union, Literal, Callable

knowledge_prompt = """Provide a detailed response on the following topic: 

{text_prompt}

Focus exclusively on the given topic and avoid including general information.
"""

database_rag_prompt = """Using the provided SQL dataset snippet:

{context}

Provide a comprehensive response to the following query:

{query}
"""

map_template = """
Given the following document:

<START OF DOCUMENT>
{docs}
<END OF DOCUMENT>

Analyze the document to determine its primary themes and provide a clear, concise summary.
"""

map_reduce_template = """
The following is a collection of summaries:

<START OF SET>
{docs}
<END OF SET>

Review these summaries and distill them into a final, cohesive summary that captures the key themes and insights.
"""

database_docstring_prompt = """You are tasked with generating comprehensive documentation for an SQL database. 

Below is a **data snippet** from the database, showing a few sample rows from each table for context:
<start of **data snippet**>
{database_snapshot}
<end of **data snippet**>

The following **knowledge base** is provided for the database:
<start of **knowledge base**>
{knowledge_base}
<end of **knowledge base**>

For each table in the database, perform the following tasks:
1. Provide the **table name** and a brief **description** of its purpose.
2. List all the **columns** in the table, including:
   - Column name.
   - Data type.
   - A brief description of what the column represents.

Ensure the output is structured clearly and follows these requirements:
{docstring_query}

Respond in markdown format using the following schema:

```markdown
### Table: <Table Name>
**Description:** <Brief description of the table>

#### Columns:
- **<Column Name>** (*<Data Type>*): <Column description>. Constraints: <Constraints if any>.
- ...
```

Reminder to always respond with valid markdown output, adhering to the specified schema.
"""

sample_sql_code = """
import sqlite3

# Connect to the database
connection = sqlite3.connect('{database_name}')

try:
    # Create a cursor object
    cursor = connection.cursor()
    
    # Define and execute the SQL query
    # The task may require multiple SQL queries executed sequentially to be fully addressed
    query = "<SQL Query>"  
    cursor.execute(query)
    
    # Fetch the column names and results
    column_names = [description[0] for description in cursor.description]  # Retrieve column names
    results = cursor.fetchall()
    
    # Print the entire retrieved data, including column headers
    if results:
        # Print the column headers
        print(f"{{' | '.join(column_names)}}")
        print("-" * (len(column_names) * 15))  # Adjust separator length dynamically

        # Print each row
        for row in results:
            print(" | ".join(map(str, row)))
    else:
        # If no rows are retrieved, print an empty table with column names
        print(f"{{' | '.join(column_names)}}")
        print("No rows retrieved. (Empty table)")

except sqlite3.Error as e:
    # Handle database errors
    print(f"An error occurred: {{e}}")

finally:
    # Ensure the database connection is closed
    connection.close()
"""

database_code_prompt = """Given the following knowledge base:
{knowledge_base}

Write a Python code specification document to retrieve data from an SQL database using the SQLite API.

The specification should:
1. Analyze the provided task prompt to identify the data retrieval requirements.
2. Define the SQL queries necessary to fulfill the data retrieval requirements.
3. Provide detailed Python code instructions to:
   - Connect to the SQLite database (assume the database file name is `{database_name}` unless specified otherwise).
   - Execute the required SQL queries to meet the task's requirements.
   - Fetch and return the retrieved results in an appropriate format, such as a structured table or a serialized dataframe.
   - Always display the entire retrieved data without skipping any rows or columns. If the query retrieves no rows, display an empty table with the relevant column names.
4. Emphasize robust error handling to address potential issues during database connection and query execution.
5. Ensure proper closure of the database connection after completing all operations.
6. Include clear, step-by-step comments throughout the code to enhance readability and maintainability.

The task to be addressed by the Python code specification:
{user_query}

Respond with a complete Python code specification document that adheres to these requirements and fully addresses the given task.
"""

python_code_prompt = """Please generate the Python code for the following code specification and sample code. \
Ensure the generated Python code is well-commented and formatted properly for readability. \
Include any necessary explanations or context within the code as markdown cells to clarify the purpose and functionality of the code.

SPECIFICATION:\n--------------\n{user_specifications}

SAMPLE CODE:\n------------\n{sample_code}

{previous_work}

Reminder to always respond with the required Python code and nothing else."""

checker_prompt = """Please check if the following Python code script executed successfully based on the output description. \
The python code script was executed in a Python 3.10 REPL shell on Ubuntu 24.04 LTS and the output is taken from the default output stream.

Python Code Script:\n-------------------\n{code_script}

Output:\n------------\n{code_output}

Respond with a single $JSON_BLOB matching the following schema:

```json
{{
    "success": <string; "true" or "false">,
    "description": <string; "program executed successfully" if "success" is "true", otherwise provide a detailed description of the issue/error>
}}
```

Reminder to always respond with a valid $JSON_BLOB and nothing else."""

repl_prompt = """Please update the following python code for execution in a Python REPL shell. \
Ensure the code is formatted properly and is without any syntax or logical errors.

CODE:\n-----\n{code_script}

All external python packages should be installed before being imported within the python script. Install any necessary python package as follows:
import subprocess
import sys
subprocess.check_call([sys.executable, "-m", "pip", "install", package])

Ensure the above code is executed before the import statement of any external python package.

For eg, to use requests and beautifulsoup python packages:
import subprocess
import sys
subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
subprocess.check_call([sys.executable, "-m", "pip", "install", "beautifulsoup4"])
import requests
from bs4 import BeautifulSoup

Ensure correct formatting of each line in the code, so that it can be saved as a '*.py' python code file for later reference. \
Do not start your response with a conversational text, such as 'Here is the required code', respond only with the required python code. \
Reminder to always respond with a valid python code script and nothing else."""

debugging_prompt = """Please correct the following Python code script based on the provided error description. \
Ensure that the corrected code addresses all the issues indicated by the error. The code will be executed in a Python REPL shell.

Python Code Script:\n-------------------\n{code_script}

Error:\n------------\n{error_trace}

{previous_work}

Ensure correct formatting of each line in the code, so that it can be saved as a '*.py' python code file for later reference. \
Do not start your response with a conversational text, such as 'Here is the updated code', only respond with the updated python code. \
Reminder to always respond with a valid python code script and nothing else."""

scraper_prompt = {
    'search_system_template': "You are an AI that assists the user with searching relevant information on the web. \
Your task is to change a given prompt into a query that is optimized for a Google search.",

    'search_human_template': "Here is the prompt: {query} \n\n\
Reword this question into a Google search query in order to find the relevant answers from the web. \
Reminder to always respond with only the required Google search query and no other conversational text."
}

JSON_PARSER_TEMPLATE = {
    'value':{
        'json_parsing_system':"""You are an advanced LLM-based JSON parser. Your task is to correctly parse a given JSON string \
and return the value of the specified key. Always respond with only the value of the specified key and nothing else.
""",
        'json_parsing_template':"""JSON string:

{json_string}

Output the value for the following key:

{key_name}

Please respond with only the value of the specified key and nothing else.
""",
    },
    'keys':{
        'json_parsing_system':"""You are an advanced LLM-based JSON parser. Your task is to correctly parse a given JSON string \
and return a list of all keys present in the JSON string. Always respond with only a comma separated list of keys and nothing else.
""",
        'json_parsing_template':"""JSON string:

{json_string}

Please respond with a comma separated list of keys and nothing else.
""",
    }
}

VERIFICATION_TEMPLATE = {
    'system': """You will receive an instruction, reasoning steps, and final answer. 
Your task is to evaluate how well the reasoning steps and final answer address the given instruction. 

Use the following Likert scale to generate a score: 
- 10: Completely addresses the question with clear and logical reasoning, leading to an accurate final answer. 
- 7-9: Addresses the question well, but there are minor gaps or ambiguities in reasoning or the final answer. 
- 4-6: Partially addresses the question; reasoning or the final answer has notable gaps or issues. 
- 1-3: Barely addresses or does not address the question; reasoning is flawed or missing, and the final answer is inaccurate. 

Always evaluate the provided reasoning steps and the final answer to determine how well they fulfill the instruction. \
Do not consider factors outside the reasoning and final answer provided. \
Ensure your explanation justifies the score based on the quality of reasoning steps and final answer in addressing the instruction.
""",
    'human': """INSTRUCTION:
{instruction_prompt}

REASONING STEPS:
{reasoning_steps}

FINAL ANSWER:
{final_answer}

Evaluate how well the reasoning steps and final answer address the given instruction.

Answer in a $JSON_BLOB, using the following schema:
```json
{{
    "score": <Likert scale score from 1 to 10>,
    "explanation": "<Reason for the selected score>"
}}
```

Reminder to ALWAYS respond with a valid $JSON_BLOB and nothing else.
"""
}