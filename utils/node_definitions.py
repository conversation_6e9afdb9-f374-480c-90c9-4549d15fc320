import numpy as np
import pickle as pkl
import json, ast, re
import tempfile, uuid, copy
import pathlib, importlib
import asyncio, subprocess

from typing import Annotated, Literal, Dict, List, Tuple, Optional
from pydantic import BaseModel, Field

from langchain_core.tools import tool
from langchain_core.prompts import <PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON>plate, MessagesPlaceholder
from langchain_core.prompt_values import ChatPromptValue
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser, JsonOutputParser
from langchain_core.runnables import RunnableLambda
from langchain.tools.render import render_text_description_and_args

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph, MessagesState
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode

from utils.agent_templates import *
from utils.tool_definitions import GLO<PERSON>L_LLM, CODING_LLM, tool_dict, custom_stop, final_answer, set_conversation_buffer, json_output_parser, verification_function

SOLVER_STRATEGY = "react"
GLOBAL_PLAN = None
LLAMA_CPP_FUNCTION_CALL = False
PREVIOUS_CONVERSATION = []

tools = [f for f in tool_dict.values()]
tool_names = [f for f in tool_dict.keys()]
tools_renderer = render_text_description_and_args(tools)
tool_node = ToolNode(tools+[final_answer])

def set_llamacpp_function_call_flag(flag: bool=True):
    global LLAMA_CPP_FUNCTION_CALL
    LLAMA_CPP_FUNCTION_CALL = flag
    return LLAMA_CPP_FUNCTION_CALL

def set_node_tools(tool_list):
    global tools, tool_names, tools_renderer, tool_node
    if "All" in tool_list:
        tool_list = [f for f in tool_dict.keys()]
    elif "direct_response" not in tool_list:
        tool_list += ["direct_response"]
    
    tools = [tool_dict[f] for f in tool_list if f in tool_dict.keys()]
    tool_names = [f for f in tool_list if f in tool_dict.keys()]
    tools_renderer = render_text_description_and_args(tools)
    tool_node = ToolNode(tools+[final_answer])
    print("Selected tools:", tool_names)
    return tools, tool_names, tools_renderer, tool_node

def set_solver_strategy(strategy: Literal["react", "plan-and-solve"]):
    global SOLVER_STRATEGY
    if strategy== "plan-and-solve":
        SOLVER_STRATEGY = "plan-and-solve"
    else:
        SOLVER_STRATEGY = "react"
    return SOLVER_STRATEGY

def set_previous_conversation(buffer):
    global GLOBAL_PLAN
    global PREVIOUS_CONVERSATION
    PREVIOUS_CONVERSATION = buffer
    GLOBAL_PLAN = None
    return PREVIOUS_CONVERSATION

class Plan(BaseModel):
    steps: List[str]

class PlanExecute(BaseModel):
    input: str
    plan: List[str]
    overall_plan: List[str]
    past_steps: List[str]
    response: List[str]
    current_step: int
    step_response: List[List[str]]
    
def get_global_plan():
    global GLOBAL_PLAN
    return GLOBAL_PLAN

def reset_global_plan():
    global GLOBAL_PLAN
    GLOBAL_PLAN = None
    return GLOBAL_PLAN

def format_aimessage_for_toolcall(ai_message: AIMessage) -> AIMessage:
    global CODING_LLM
    tool_message = AIMessage(content="")
    if ai_message.additional_kwargs.get("tool_calls", None):
        tool_message.additional_kwargs = ai_message.additional_kwargs
        tool_message.tool_calls = [{
            "name": ai_message.tool_calls[f]['name'],
            "args": ai_message.tool_calls[f]['args'],
            "id": ai_message.tool_calls[f]['id'],
            "type": "tool_call",
        } for f in range(len(ai_message.tool_calls))]
        tool_message.id = ai_message.id
        tool_message.response_metadata = ai_message.response_metadata
        return tool_message
    
    message_dict = json_output_parser(CODING_LLM, ai_message.content, required_keys=["name", "arguments"])
    if type(message_dict["arguments"])==str:
        argument_dict = json_output_parser(CODING_LLM, message_dict["arguments"], required_keys=None, force_parse=False)
        # temp_argument_dict = json_output_parser(CODING_LLM, message_dict["arguments"], required_keys=None, force_parse=False)
        # argument_dict = {}
        # for argument_key in temp_argument_dict.keys():
        #     argument_dict[argument_key.replace(' ', '').replace('__', '_')] = temp_argument_dict[argument_key]
    elif type(message_dict["arguments"])==dict:
        argument_dict = message_dict["arguments"]
    else:
        e = type(message_dict["arguments"])
        raise ValueError(f"argument type is unknown: {e}")
    print(argument_dict)
    
    tool_id = f"call_{uuid.uuid4()}"
    tool_message.additional_kwargs = { "tool_calls":[{
        "id": tool_id,
        "function": {
            "name": message_dict["name"], 
            "arguments": message_dict["arguments"] if type(message_dict["arguments"])==str else json.dumps(message_dict["arguments"])
            # "arguments": argument_dict
            },
        "type": "function",
    }]}
    tool_message.tool_calls = [{
        "name": message_dict["name"],
        "args": argument_dict,
        "id": tool_id,
        "type": "tool_call",
    }]
    tool_message.id = ai_message.id
    tool_message.response_metadata = ai_message.response_metadata
    # print(tool_message)
    return tool_message

def agent_strategy_node(state: MessagesState) -> Literal["react_agent", "plan_and_solve_agent"]:
    global SOLVER_STRATEGY
    
    if SOLVER_STRATEGY=="react":
        return "react_agent"
    elif SOLVER_STRATEGY=="plan-and-solve":
        return "plan_and_solve_agent"
    else:
        print(f"Selected solver strategy is {SOLVER_STRATEGY}. Should be one of ['react', 'plan-and-solve']")
    return "react_agent"

def agent_has_finished(state: MessagesState, messages_key: str = "messages",) -> Literal["tool_node", "plan_and_solve_agent", "verification_node"]:
    global SOLVER_STRATEGY, GLOBAL_PLAN
    
    if isinstance(state, list):
        ai_message = state[-1]
    elif isinstance(state, dict) and (messages := state.get(messages_key, [])):
        ai_message = messages[-1]
    elif messages := getattr(state, messages_key, []):
        ai_message = messages[-1]
    else:
        raise ValueError(f"No messages found in input state to tool_edge: {state}")
    
    if SOLVER_STRATEGY=="plan-and-solve" and GLOBAL_PLAN:
        if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0 and ai_message.tool_calls[-1]["name"]!="final_answer":
            return "tool_node"
        if GLOBAL_PLAN.current_step!=len(GLOBAL_PLAN.overall_plan):
            if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0 and ai_message.tool_calls[-1]["name"]=="final_answer":
                GLOBAL_PLAN.response += [json.dumps(ai_message.tool_calls[-1]["args"])]
            else:
                GLOBAL_PLAN.response += [ai_message.content]
            return "plan_and_solve_agent"
        else:
            GLOBAL_PLAN.step_response.append([f.content for f in state["messages"][1:][len(GLOBAL_PLAN.step_response[-1]):]])
            return "verification_node"
    elif hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0:
        if ai_message.tool_calls[-1]["name"]=="final_answer":
            return "verification_node"
        return "tool_node"
    return "verification_node"

def verification_node(state: MessagesState) -> Dict:
    global GLOBAL_LLM
    print("Verifying final answer...")
    set_conversation_buffer([f.content for f in state["messages"][1:-1]])
    response_dict = verification_function(instruction_prompt=state["messages"][0], final_answer=state["messages"][-1])
    response = AIMessage(content="The final answer has a confidence of **'{}'** on a scale of 1 to 10. \n\nExplanation: {}".format(response_dict["score"], response_dict["explanation"]))
    return {"messages": [response]}

def tool_calling_agent(state: MessagesState) -> Dict:
    global CODING_LLM
    set_conversation_buffer([f.content for f in state["messages"][1:-1]])

    tool_message = state["messages"][-1]
    tool_prompt = ChatPromptTemplate.from_messages([
        ("system", TOOL_PROMPT_SYSTEM),
        ("human", TOOL_PROMPT_TEMPLATE),
    ])
    # TOOLS_LLM = CODING_LLM.bind_tools(tools+[final_answer])
    # tool_model = (tool_prompt | TOOLS_LLM)
    
    def check_action_key(action_message):
        for k in custom_stop["action"]:
            if k in action_message:
                return k
        return False
    
    tool_message = tool_message.content
    try:
        if action := check_action_key(tool_message):
            tool_message_str = tool_message[tool_message.find(action)+len(action):]
        else:
            tool_message_str = copy.deepcopy(tool_message)
        parsed_dict = json_output_parser(CODING_LLM, tool_message_str)
        # tool_call_dict = {
        #     "name": parsed_dict['action'].replace(' ', '').replace('__', '_'),
        #     "arguments": parsed_dict["action_input"],
        # }
        if "final_answer" in parsed_dict['action']:
            return {"messages": [AIMessage(content=parsed_dict['action_input'])]}
        # elif parsed_dict["action"] in tool_dict.keys():
        #     tool_call_dict = json.dumps(tool_call_dict)
        #     response = format_aimessage_for_toolcall(AIMessage(content=f'<tool_call> {tool_call_dict} </tool_call>'))
        #     return {"messages": [response]}
    except Exception as e:
        print(f"Parser failed with error: {e}\n{tool_message_str}")
        if action := check_action_key(tool_message):
            tool_message_str = tool_message[tool_message.find(action)+len(action):]
        else:
            print(f"Tool calling agent failed with message: {tool_message}")
            return {"messages": [AIMessage(content=tool_message)]}
    
    if LLAMA_CPP_FUNCTION_CALL:
        print("Using llama.cpp function call type.")
        TOOLS_LLM = CODING_LLM.bind_tools(tools=tools+[final_answer], tool_choice="any")
    else:
        TOOLS_LLM = CODING_LLM.bind_tools(tools+[final_answer])
    tool_model = (tool_prompt | TOOLS_LLM)
    response = tool_model.invoke({
        "tool_call": tool_message_str,
    })
    response = format_aimessage_for_toolcall(response)
    return {"messages": [response]}

def react_agent(state: MessagesState) -> Dict:
    global PREVIOUS_CONVERSATION
    global GLOBAL_LLM
    global SOLVER_STRATEGY, GLOBAL_PLAN
    
    def find_last_occurrence(lst, target):
        # try:
        #     position = len(lst) - lst[::-1].index(target) - 1
        #     return position
        # except ValueError:
        #     return 0
        for l_idx in range(len(lst)-1, -1, -1):
            if lst[l_idx].endswith(target):
                return l_idx
        return 0
    
    plan_str = None
    llm_with_stop = GLOBAL_LLM.bind(stop=custom_stop['observation']+custom_stop['plan'])
    executor_system_prompt = '\n'.join([EXECUTE_STEP_SYSTEM, FUNCTION_PROMPT_TEMPLATE, RESPONSE_FORMAT_SYSTEM])
    response_messages = state["messages"][1:]
    last_plan_state_index = find_last_occurrence([f.content for f in response_messages], custom_stop['plan'][0])
    
    if GLOBAL_PLAN:
        previous_steps = "\n".join([f"STEP {i+1}. {step} \nOUTPUT for STEP {i+1}: {GLOBAL_PLAN.response[i]}" \
            for i, step in enumerate(GLOBAL_PLAN.past_steps)])
        plan_str = STEP_PROMPT_TEMPLATE.format(
            objective=GLOBAL_PLAN.input,
            previous_steps=previous_steps if len(previous_steps.strip())>0 else "No previous steps executed.",
            current_step=GLOBAL_PLAN.overall_plan[GLOBAL_PLAN.current_step-1]
        )
        if GLOBAL_PLAN.current_step>1 and state["messages"][-1].content.endswith(custom_stop['plan'][0]):
            GLOBAL_PLAN.step_response.append([f.content for f in state["messages"][1:][len(GLOBAL_PLAN.step_response[-1]):last_plan_state_index]])
    
    react_prompt = ChatPromptTemplate.from_messages([
        ("system", executor_system_prompt),
        MessagesPlaceholder(variable_name="previous_conversation", optional=True),
        ])
    react_prompt_with_tools = react_prompt.partial(tools=tools_renderer, tool_names=tool_names)
    react_prompt_with_tools_value = react_prompt_with_tools.invoke({
        "previous_conversation": PREVIOUS_CONVERSATION
    })
    # print("\n".join([f.content for f in react_prompt_with_tools.invoke({}).messages[1:]]))
    
    react_prompt_with_tools_value = ChatPromptValue(messages = \
        react_prompt_with_tools_value.to_messages() + \
        # ([("human", plan_str)] if plan_str else [("human", HUMAN_PROMPT_TEMPLATE.format(input=state["messages"][0].content))]) + \
        ([HumanMessage(plan_str)] if plan_str else [HumanMessage(HUMAN_PROMPT_TEMPLATE.format(input=state["messages"][0].content))]) + \
        [f for f in response_messages[last_plan_state_index:] if not f.content.endswith(custom_stop['plan'][0])]
    )

    # react_model = (react_prompt_with_tools | llm_with_stop)
    response = llm_with_stop.invoke(react_prompt_with_tools_value)
    response.content = f"{response.content}\n\nObservation: "
    return {"messages": [response]}

def plan_and_solve_agent(state: MessagesState) -> Dict:
    global GLOBAL_LLM
    global GLOBAL_PLAN
    
    def plan_parser(text: AIMessage) -> Plan:
        steps = [v for v in re.split("\n\s*\d+\. ", text.content)[1:]]
        return Plan(steps=steps)
    
    llm_with_stop = GLOBAL_LLM.bind(stop=custom_stop['observation']+custom_stop['plan'])
    plan_prompt = ChatPromptTemplate.from_messages([
        ("system", PLANNING_STEP_SYSTEM),
        MessagesPlaceholder(variable_name="messages", optional=True)
    ])
    plan_prompt_with_tools = plan_prompt.partial(tools=tools_renderer, tool_names=tool_names)
    plan_model = (plan_prompt_with_tools | llm_with_stop | RunnableLambda(plan_parser))
    
    if len(state["messages"])>1:
        input_dict = {
            "messages": [("human", PLANNING_CORRECTION_TEMPLATE.format(**{
                "input": GLOBAL_PLAN.input,
                "plan": GLOBAL_PLAN.overall_plan,
                "future_step": GLOBAL_PLAN.current_step+1,
                "current_step": GLOBAL_PLAN.current_step,
                "intermediate_steps": '\n'.join([f"\nOUTPUT {i_f+1}: {GLOBAL_PLAN.response[i_f]}" \
                    for i_f, f in enumerate(GLOBAL_PLAN.response)]),}))]
        }
    else:
        input_dict = {
            "messages": [("human", PLANNING_STEP_TEMPLATE.format(input=state["messages"][0].content))],
        }
    planner = plan_model.invoke({
        "messages": input_dict["messages"],
    })
    if GLOBAL_PLAN and len(planner.steps)<len(GLOBAL_PLAN.overall_plan):
        planner.steps = GLOBAL_PLAN.overall_plan
    
    if GLOBAL_PLAN is None:
        GLOBAL_PLAN = PlanExecute(
            input = state["messages"][0].content,
            plan = planner.steps,
            overall_plan = planner.steps,
            current_step = 1,
            past_steps = [],
            response = [],
            step_response = [[]],
        )
    else:
        GLOBAL_PLAN = PlanExecute(
            input = GLOBAL_PLAN.input,
            plan = planner.steps[GLOBAL_PLAN.current_step:],
            overall_plan = planner.steps,
            past_steps = GLOBAL_PLAN.plan[:GLOBAL_PLAN.current_step],
            current_step = GLOBAL_PLAN.current_step+1,
            response = GLOBAL_PLAN.response,
            step_response = GLOBAL_PLAN.step_response,
        )
    # print(GLOBAL_PLAN)
    return {"messages": [AIMessage(content="\n".join([f"{i+1}. {f}" for i, f in \
        enumerate(GLOBAL_PLAN.overall_plan)])+custom_stop['plan'][0])]}