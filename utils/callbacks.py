import os, sys, asyncio
import json, uuid, re, copy    
from uuid import UUID
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Sequence, TypeVar, Union, Literal, Callable

from langchain.memory import ConversationBufferMemory
from langchain.tools.render import render_text_description_and_args
from langchain.agents.format_scratchpad import format_log_to_str

from langchain_core.prompt_values import Chat<PERSON>romptValue
from langchain_core.callbacks.base import BaseCallbackHandler
from langchain_core.outputs.generation import GenerationChunk
from langchain_core.outputs.chat_generation import ChatGenerationChunk

GLOBAL_VERBOSE: Optional[bool] = False
GLOBAL_STREAM: Optional[List[str]] = None
GLOBAL_TOOL_MEMORY: Optional[List[Dict[str, Dict['str', 'str']]]] = []
GLOBAL_CONVERSATION_MEMORY: Optional[ConversationBufferMemory] = None

class IntermediateStepsCallback(BaseCallbackHandler):
    def __init__(self):
        super(IntermediateStepsCallback, self).__init__()
        self.intermediate_steps = []
        self.step_memory = []
        self.local_stream = [""]
        self.verbose = GLOBAL_VERBOSE
        
    def init_global_stream(self):
        global GLOBAL_STREAM
        GLOBAL_STREAM = [""]
        self.local_stream = [""]
        return GLOBAL_STREAM
        
    def get_global_stream(self):
        global GLOBAL_STREAM
        return GLOBAL_STREAM
    
    def add_to_global_stream(self, tokens):
        global GLOBAL_STREAM
        GLOBAL_STREAM.append(tokens)
        return GLOBAL_STREAM
    
    def memory_to_string(self, include_intermediate_steps=False, include_step_answers=False):
        if len(self.step_memory)==0 or len(self.intermediate_steps)==0:
            return ""
        final_answer = self.step_memory[0]
        self.intermediate_steps = [f for f in self.intermediate_steps if len(f.strip())>0]
        intermediate_steps = '\n'.join([f'{self.intermediate_steps[f]}' for f in range(len(self.intermediate_steps))])
        self.step_memory = self.step_memory[1:-1]
        
        final_answer += \
            ('\n\n' + '\n\n'.join([f'{self.step_memory[f]}' for f in range(max(0, len(self.step_memory)-1))])\
                if include_step_answers else '') + \
            ('\n\nIntermediate Steps: \n{} '.format(intermediate_steps)\
                if (len(intermediate_steps)>0 and include_intermediate_steps) else '') + \
            '\n\nFinal Answer: \n'
        return final_answer
    
    def on_tool_end(
        self, 
        output: Any, 
        *, run_id: UUID, 
        parent_run_id: Optional[UUID] = None, 
        **kwargs: Any) -> Any:
    
        global GLOBAL_STREAM
        GLOBAL_STREAM.append(str(output))
        return
    
    def on_llm_new_token(
        self, 
        token: str, 
        *, chunk: Optional[Union[GenerationChunk, ChatGenerationChunk]] = None, 
        run_id: UUID, 
        parent_run_id: Optional[UUID] = None, 
        **kwargs: Any) -> Any:
        
        global GLOBAL_STREAM
        GLOBAL_STREAM.append(token)
        self.local_stream.append(chunk)
        return
    
def set_global_tool_memory(memory_var=None):
    global GLOBAL_TOOL_MEMORY
    if memory_var is not None:
        GLOBAL_TOOL_MEMORY = copy.deepcopy(memory_var)
    return GLOBAL_TOOL_MEMORY

def set_global_conversation_memory(memory_var, prompt=None):
    global GLOBAL_CONVERSATION_MEMORY
    if memory_var is not None:
        GLOBAL_CONVERSATION_MEMORY = copy.deepcopy(memory_var)
    if prompt is not None:
        GLOBAL_CONVERSATION_MEMORY.chat_memory.add_user_message(prompt)
    return GLOBAL_CONVERSATION_MEMORY

def get_tool_information(tool_list):
    tools_renderer = render_text_description_and_args(tool_list)
    return tools_renderer
    
def memory_to_chat_history(memory_var):
    if memory_var is None:
        return ""
    elif len(memory_var.chat_memory.messages)==0:
        return ""
    else:
        chat_history = ChatPromptValue(messages=memory_var.chat_memory.messages)
        chat_history = chat_history.to_string()
    return chat_history

def tool_memory_to_previous_work(tool_memory, intermediate_steps=False):
    intermediate_steps = []
    for i in range(len(tool_memory)):
        tool_key = list(tool_memory[i].keys())[0]
        intermediate_steps.append(
            "Action_{:02}: \n```json\n{}\n```\nObservation_{:02}: {}".format(
                i, json.dumps({"action": tool_key,
                            "action_input": tool_memory[i][tool_key]['input']}),
                i, tool_memory[i][tool_key]['output'])
        )
    if intermediate_steps:
        return "\n".join(intermediate_steps) + "\n\n**Final Answer**: \n\n"
    return "\n\n".join(intermediate_steps)

def intermediate_steps_to_chat_memory(llm_output, memory_var):
    ai_message = ''
    assert memory_var is not None
    if 'tool_intermediate_steps' in llm_output.keys():
        if type(llm_output['tool_intermediate_steps'])==str and len(llm_output['tool_intermediate_steps'])>0:
            ai_message = llm_output['tool_intermediate_steps']
        elif len(llm_output['tool_intermediate_steps'])>0:
            ai_message = format_log_to_str(llm_output['tool_intermediate_steps'], llm_prefix=' \n\n') + '\n\n**Final Answer**: \n\n'
    
    assert len(llm_output['input'])%2==1
    for i in range(len(llm_output['input'])):
        if llm_output['input'][i][0]=='human':
            memory_var.chat_memory.add_user_message(llm_output['input'][i][1])
        if llm_output['input'][i][0]=='ai':
            memory_var.chat_memory.add_ai_message(llm_output['input'][i][1])
    memory_var.chat_memory.add_ai_message(ai_message+llm_output['output'])
    return memory_var