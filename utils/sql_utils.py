import sqlite3
import pandas as pd
import os, sys, re, json
import io, tqdm

from os.path import isfile, isdir, join
from typing import Dict, Optional
from langchain_chroma import Chroma
from langchain_core.embeddings import Embeddings
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain.text_splitter import RecursiveCharacterTextSplitter

from rag.cmrag import DocumentLoader, RAG_TOP_K, RAG_SCORE_CUTOFF
from utils.crag_templates import query_system_template, query_human_template, answer_system_template, answer_human_template

SQL_VECTORSTORE = None
SQL_RETRIEVER = None
SQL_DATABASE_NAME = None
SQL_KNOWLEDGE_BASE = None

def json_output_parser(llm_json_output, delimiter_start="{", delimiter_end="}", verbose=False):
    if verbose:
        print('LLM output of data-type', type(llm_json_output))
        print(llm_json_output)
    if (delimiter_start in llm_json_output) and (delimiter_end in llm_json_output):
        try:
            start_offset = llm_json_output.find(delimiter_start)
            end_offset = llm_json_output.rfind(delimiter_end)+len(delimiter_end)
            json_dict = json.loads(llm_json_output[start_offset:end_offset])
            return json_dict
        except Exception as e:
            print(f"JSON parser failed with error: {e}")
            start_offset = llm_json_output.find(delimiter_start)
            end_offset = llm_json_output.rfind(delimiter_end)+len(delimiter_end)
            json_dict = ast.literal_eval(llm_json_output[start_offset:end_offset])
            return json_dict
    else:
        return {}

def save_dataframes_to_sqlite(dataframes, table_names, database_name):
    """
    Save a list of pandas DataFrames to a SQLite database.

    Parameters:
        dataframes (list of pd.DataFrame): List of DataFrames to save.
        table_names (list of str): List of table names corresponding to each DataFrame.
        database_name (str): Name of the SQLite database file.
    """
    if len(dataframes) != len(table_names):
        raise ValueError("The number of DataFrames and table names must be the same.")

    conn = sqlite3.connect(database_name)
    try:
        for df, table_name in zip(dataframes, table_names):
            df.to_sql(table_name, conn, if_exists='replace', index=False)
            print(f"Saved DataFrame to table '{table_name}' in database '{database_name}'.")
            yield f"Saved DataFrame to table '{table_name}' in database '{database_name}'."
    except Exception as e:
        print(f"An error occurred: {e}")
        yield f"An error occurred: {e}"
    finally:
        conn.close()

def read_vcf(file, load_any=False):
    with open(file, 'r') as f:
        lines = [l[1:] if l.startswith('#') else l for l in f if not l.startswith('##')]
    if load_any:
        return pd.read_csv(io.StringIO(''.join(lines)), sep='\t')
    return pd.read_csv(
        io.StringIO(''.join(lines)),
        dtype={'CHROM': str, 'POS': str, 'ID': str, 'REF': str, 'ALT': str,
                'QUAL': str, 'FILTER': str, 'INFO': str, 'FORMAT': str, 'SAMPLE': str },
        sep='\t'
    ).rename(columns = {'CHROM': 'CHR'})

def patient_filtering(data_vcf):
    ## "Identifier" is a created ID for the variant. It contains build from 4 columns; CHR:POS:REF:ALT. The intention with this column is to use it as a column to merge variants on from once own dataset. 
    print("Task[0/7] - Creating column: Identifier")
    yield "Task[0/7] - Creating column: Identifier"
    data_vcf["Identifier"] = data_vcf["CHR"] +":"+ data_vcf["POS"] +":"+ data_vcf["REF"] +":"+ data_vcf["ALT"]
    data_vcf["Locus"] = data_vcf["POS"]
    
    order_columns = ["Identifier", "CHR", "POS", "REF", "ALT", "ID", "QUAL", "FILTER", "INFO", "FORMAT", "SAMPLE"]
    clinVar_final = data_vcf.copy()
    clinVar_final = clinVar_final[order_columns]
    yield clinVar_final

def clinvar_filtering(data_vcf):
    # Clinical significance
    print("Task[1/7] - Creating column: Clinical_significance")
    yield "Task[1/7] - Creating column: Clinical_significance"
    data_vcf["Clinical_significance"] = data_vcf["INFO"].apply(lambda x: re.findall(r"(?<=CLNSIG).*?(?=;)", x)).astype("str")
    data_vcf["Clinical_significance"] = [re.sub(r'[^a-zA-Z/.]+', '_', s) for s in data_vcf["Clinical_significance"]]
    data_vcf["Clinical_significance"] = data_vcf["Clinical_significance"].apply(lambda x: x.rstrip("_")).apply(lambda x: x.lstrip("_")).apply(lambda x: x.replace("_", " "))

    # Gene symbol 
    print("Task[2/7] - Creating column: Gene_symbol ")
    yield "Task[2/7] - Creating column: Gene_symbol "
    data_vcf["Gene_symbol"] = data_vcf["INFO"].apply(lambda x: re.findall(r"(?<=GENEINFO).*?(?=;)", x)).astype("str")
    data_vcf["Gene_symbol"] = data_vcf["Gene_symbol"].apply(lambda x: x.split(":")[0]).apply(lambda x: x.replace("['=", ""))

    # ";" is added to the end of the "INFO" column to have a end mark. This is needed as re.findall finds "something" in between two patterns. " " can not be used. 
    data_vcf["INFO"] = data_vcf["INFO"].apply(lambda x: x+";")

    # RS_ids
    print("Task[3/7] - Creating column: RS_id")
    yield "Task[3/7] - Creating column: RS_id"
    data_vcf["RS_id"] = data_vcf["INFO"].apply(lambda x: re.findall(r"(?<=RS).*?(?=;)", x)).astype("str").apply(lambda x: re.sub('[^0-9]',"", x)).astype("str")
    data_vcf["RS_id"] = ["RS"+x if len(x) > 1 else x for x in data_vcf["RS_id"]]

    ## Mutation type of the variant 
    print("Task[4/7] - Creating column: Mutation_type")
    yield "Task[4/7] - Creating column: Mutation_type"
    data_vcf["Mutation_type"] = data_vcf["INFO"].apply(lambda x: re.findall(r"(?<=MC).*?(?=;)", x)).astype("str")
    data_vcf["Mutation_type"] = data_vcf["Mutation_type"].apply(lambda x: x.split("|")[-1]).apply(lambda x: x[0:-2])
        
    ## ClinVar review status
    print("Task[5/7] - Creating column: ClinVar_review_status")
    yield "Task[5/7] - Creating column: ClinVar_review_status"
    data_vcf["ClinVar_review_status"] = data_vcf["INFO"].apply(lambda x: re.findall(r"(?<=CLNREVSTAT).*?(?=;)", x)).astype("str")
    data_vcf["ClinVar_review_status"] = data_vcf["ClinVar_review_status"].apply(lambda x: x.replace("['=", "")).apply(lambda x: x.replace("']", ""))
    
    ## Disease associations
    print("Task[6/7] - Creating column: ClinVar_disease_name")
    yield "Task[6/7] - Creating column: ClinVar_disease_name"
    data_vcf["ClinVar_disease_name"] = data_vcf["INFO"].apply(lambda x: re.findall(r"(?<=CLNDN).*?(?=;)", x)).astype("str")
    data_vcf["ClinVar_disease_name"] = data_vcf["ClinVar_disease_name"].apply(lambda x: x.replace("['=", "")).apply(lambda x: x.replace("']", ""))        

    ## "Identifier" is a created ID for the variant. It contains build from 4 columns; CHR:POS:REF:ALT. The intention with this column is to use it as a column to merge variants on from once own dataset. 
    print("Task[7/7] - Creating column: Identifier")
    yield "Task[7/7] - Creating column: Identifier"
    data_vcf["Identifier"] = data_vcf["CHR"] +":"+ data_vcf["POS"] +":"+ data_vcf["REF"] +":"+ data_vcf["ALT"]
    
    order_columns = ["Identifier", "Gene_symbol", "Clinical_significance", "RS_id", "Mutation_type", "ClinVar_review_status", "ClinVar_disease_name", "CHR", "POS", "REF", "ALT", "ID", "QUAL", "FILTER", "INFO"]
    clinVar_final = data_vcf.copy()
    clinVar_final = clinVar_final[order_columns]
    yield clinVar_final

def get_sql_knowledge_base(verbose: bool = False):
    global SQL_DATABASE_NAME, SQL_KNOWLEDGE_BASE
    if verbose:
        print(SQL_DATABASE_NAME)
        print(SQL_KNOWLEDGE_BASE)
    return SQL_DATABASE_NAME, SQL_KNOWLEDGE_BASE

def get_sql_vectorstore_and_retriever():
    global SQL_VECTORSTORE, SQL_RETRIEVER
    return SQL_VECTORSTORE, SQL_RETRIEVER

async def create_bio_db(sql_yml: Dict[str, str], text_embedding_model: Embeddings, build_db=True, verbose: bool = False) -> str:
    global SQL_DATABASE_NAME, SQL_KNOWLEDGE_BASE
    global SQL_VECTORSTORE, SQL_RETRIEVER
    
    yield "Creating SQL knowledge vectorstore and database"
    data_prefix = sql_yml["data_prefix"]
    patient_file = sql_yml["patient_file"]
    clinvar_file = sql_yml["clinvar_file"]
    database_files = sql_yml["database_files"]
    knowledge_files = sql_yml["knowledge_files"]
    SQL_DATABASE_NAME = join(data_prefix, sql_yml["database_name"]+".db")
    
    
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=4096,
        chunk_overlap=256
    )
    persist_directory = join(data_prefix if isdir(data_prefix) else os.getenv('CACHE'), "sql_knowledge_base")
    if not isdir(persist_directory):
        os.mkdir(persist_directory)
    # else:
    #     os.system(f"rm -r '{persist_directory}'")
    #     os.mkdir(persist_directory)
    #     print("Reset SQL Knowledge Base")
    
    SQL_VECTORSTORE = Chroma(
        collection_name="sql_knowledge_base",
        embedding_function=text_embedding_model,
        persist_directory=persist_directory,
    )
    SQL_VECTORSTORE.reset_collection()
    SQL_RETRIEVER = SQL_VECTORSTORE.as_retriever(search_kwargs={"k": int(RAG_TOP_K*4)})
    print("Initialized SQL Vectorstore")
    docs = [DocumentLoader.load_document(join(data_prefix, knowledge_file)) for knowledge_file in knowledge_files]
    try:
        for doc in docs:
            splits = text_splitter.split_documents(doc)
            split_ids = SQL_VECTORSTORE.add_documents(splits)
        if verbose:
            print(split_ids)
        print("Created SQL Vectorstore")
    except Exception as e:
        print(f"SQL creation failed with error: {e}")
    
    if not build_db:
        yield "Using pre-initialized database values\n\n"
        if isfile(join(f"{os.getenv('CACHE')}", "downloads", f"{SQL_DATABASE_NAME}.txt")):
            with open(join(f"{os.getenv('CACHE')}", "downloads", f"{SQL_DATABASE_NAME}.txt"), 'r') as fp:
                SQL_KNOWLEDGE_BASE = fp.read()
        else:
            SQL_KNOWLEDGE_BASE = None
    else:
        if isfile(join(f"{os.getenv('CACHE')}", "downloads", f"{SQL_DATABASE_NAME}.txt")):
            os.unlink(join(f"{os.getenv('CACHE')}", "downloads", f"{SQL_DATABASE_NAME}.txt"))
            
        df_arr = []
        SQL_KNOWLEDGE_BASE = None
        csv_file_names = [f for f in database_files if isfile(join(data_prefix, f)) and f.split('.')[-1].lower() in ['csv']]
        for f_idx in range(len(csv_file_names)):
            file_path = join(data_prefix, csv_file_names[f_idx])
            yield f"Reading {file_path}\n\n"
            df = pd.read_csv(file_path, sep=',')
            df_arr.append((df, csv_file_names[f_idx].split('.')[0]))
            
        vcf_file_names = [f for f in database_files if isfile(join(data_prefix, f)) and (f.split('.')[-1].lower() in ['vcf']) and (f not in [patient_file, clinvar_file])]
        # vcf_file_names = [f for f in os.listdir(data_prefix) if isfile(join(data_prefix, f)) and (f.split('.')[-1].lower() in ['vcf'])]
        for f_idx in range(len(vcf_file_names)):
            file_path = join(data_prefix, vcf_file_names[f_idx])
            yield f"Reading {file_path}\n\n"
            df = read_vcf(file_path, load_any=True)
            df_arr.append((df, vcf_file_names[f_idx].split('.')[0]))
        
        try:
            yield f"Reading {join(data_prefix, patient_file)}\n\n"
            patient_df = read_vcf(join(data_prefix, patient_file), load_any=True)
            print(patient_df)
            # for yval in patient_filtering(patient_df):
            #     if type(yval)==str:
            #         yield yval
            #     else:
            #         patient_df_filtered = yval
            #         df_arr.append((patient_df_filtered, "Patient"))
            df_arr.append((patient_df, "Patient"))
        except Exception as e:
            print("Patient VCF not found.")
        
        try:
            yield f"Reading {join(data_prefix, clinvar_file)}\n\n"
            clinvar_df = read_vcf(join(data_prefix, clinvar_file), load_any=True)
            print(clinvar_df)
            # for yval in clinvar_filtering(clinvar_df):
            #     if type(yval)==str:
            #         yield yval
            #     else:
            #         clinvar_df_filtered = yval
            #         df_arr.append((clinvar_df_filtered, "ClinVar"))
            df_arr.append((clinvar_df, "ClinVar"))
        except Exception as e:
            print("ClinVar VCF not found.")
        
        if len(df_arr)>0:
            for yval in save_dataframes_to_sqlite([f[0] for f in df_arr], [g[1] for g in df_arr], SQL_DATABASE_NAME):
                yield yval
        else:
            yield f"No CSV/VCF data found in {data_prefix}\n\n"

async def sql_rag_response(query, retriever, llm, callback_manager=None):
    query_prompt = ChatPromptTemplate.from_messages([
        ('system', query_system_template),
        ('human', query_human_template)
    ])
    answer_prompt = ChatPromptTemplate.from_messages([
        ('system', answer_system_template),
        ('human', answer_human_template)
    ])
    query_changer_chain = (query_prompt | llm | StrOutputParser())
    answer_agent_chain = (answer_prompt | llm | StrOutputParser())
    
    response = await query_changer_chain.ainvoke({
        'question': query,
    }, {"callbacks": callback_manager})
    reworded_query = json_output_parser(response)["query"]
    docs = retriever.invoke(reworded_query)
    docs = "\n\n".join([f.page_content for f in docs])
    answer = await answer_agent_chain.ainvoke({
        'context': docs,
        'question': query,
    }, {"callbacks": callback_manager})
    return answer