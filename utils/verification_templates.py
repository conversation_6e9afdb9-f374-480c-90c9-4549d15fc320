llm_checker_templates = {
    'system': """You are a model designed to verify the correctness of a user's answer by comparing it with the provided correct answer. 
Your task is to determine whether the user's answer is correct by checking if it matches the correct answer. 
""",
    'user': """Compare the following user answer with the provided correct answer. \
Determine the match as 'True' or 'False' based on whether the user's answer matches the correct answer or not, respectively.

Question: {question}

User's Answer: {predicted_answer}

Correct Answer: {ground_truth}

Respond with a single $JSON_BLOB matching the following schema:\n\n\
```json\n\
{{\n\
  "match": <'True' or 'False' based on whether the user's answer matches the correct answer or not>,
  "reason": <a brief explanation for the selected 'True' or 'False' option>
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB and nothing else.
"""
}

qa_checker_templates = {
  'system': """You are an evaluator tasked with verifying whether a submitted answer contains any keywords from an accepted list of correct answers. \
You will be provided with a user's answer and a list of accepted keywords or phrases. \
Your goal is to check if any of the accepted keywords or phrases appear in the user's answer. \
If the user's answer contains one or more of these keywords or phrases, you will consider it correct.

When responding:

If the answer contains any of the accepted keywords, "match" is True.
If the answer does not contain any of the accepted keywords, "match" is False.
""",
  'user': """Determine if the answer is correct based on the presence of the accepted keywords or phrases.

Question: {question}

User's Answer: {predicted_answer}

Correct Keywords: {ground_truth}

Respond with a single $JSON_BLOB matching the following schema:\n\n\
```json\n\
{{\n\
  "match": <'True' or 'False' based on whether the user's contains any of the accepted keywords>,
  "reason": <a brief explanation for the selected 'True' or 'False' option>
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB and nothing else.
  """
}

bio_checker_templates = {
    'system': """You are a model designed to verify the correctness of a biography by comparing it with a provided text. 
Your task is to determine whether the biography is correct by checking if it is about the same person and contains information provided in the text. 
""",
    'user': """Compare the following biography with the provided text. \
Determine the match as 'True' or 'False' based on whether the biography matches the provided details in the text or not, respectively. \
Answer the match as 'True' even if the biography contains additional information not present in the provided text. \
Answer the match as 'False' only if the biography is about a different person or is missing information from the provided text.

Provided Text: \n{ground_truth}

Biography: \n{predicted_answer}

Respond with a single $JSON_BLOB matching the following schema:\n\n\
```json\n\
{{\n\
  "match": <'True' or 'False'>,
  "reason": <a brief explanation for the selected 'True' or 'False' option>
}}\n\
```\n\n\
Reminder to ALWAYS respond with a valid $JSON_BLOB and nothing else.
"""
}

llm_answer_template = """
Please provide an answer to the following question:

Question: {question}

Focus exclusively on the given question and avoid including general information.
"""