from pydantic import BaseModel, Field
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Sequence, TypeVar, Union, Literal, Callable

PLANNING_STEP_SYSTEM = """Construct a detailed, numbered, step-by-step PLAN to solve the given TASK using the provided TOOLS. \
Do not solve the TASK or provide any code, only construct the PLAN. \
This plan should involve individual tasks, that if executed correctly will yield the correct answer. Do not add any superfluous steps. \
The result of the final step should be the final answer. Make sure that each step has all the information needed - do not skip steps.

You are limited to using only the following TOOLS to help in solving the given TASK:

{tool_names}

The description for each tool is as follows:

{tools}

Output the PLAN starting with the header 'PLAN:' followed by a numbered list of steps (e.g., 1. 2. 3.). Do not add any unnecessary steps. \
The final step should almost always be 'Based on the above steps taken, please provide a conclusive answer for the original TASK. \
Ensure that it includes comprehensive explanations, key details, and any important examples or context described by the intermediate steps'. \
At the end of the PLAN, write '<END_OF_PLAN>' on a new line (without a number). \
Always provide at least a one step PLAN, even if the TASK appears simple.
"""

PLANNING_STEP_TEMPLATE = """Provide a detailed, numbered, step-by-step PLAN for solving the following TASK:

{input}"""

PLANNING_CORRECTION_TEMPLATE = """For the following TASK:

{input}

Update the current PLAN starting from STEP **{future_step}**:

CURRENT PLAN:
-------------

{plan}

The CURRENT PLAN upto STEP **{current_step}** is complete and the output for each completed step is as follows:

{intermediate_steps}

Provide an updated plan accordingly and fill out the plan for upcoming steps if required. \
Do not modify previously completed steps and the final step in the original plan when updating the plan. \
Do not reduce the number of steps in the updated plan, only add/modify future steps required to complete the task. \
Ensure that each step of the updated plan starts with a number (e.g., 1. 2. 3.). \
The final step should almost always be 'Based on the above steps taken, please provide a conclusive answer for the original TASK. \
Ensure that it includes comprehensive explanations, key details, and any important examples or context described by the intermediate steps'.
"""

EXECUTE_STEP_SYSTEM = """You are an AI Assistant built to support users with diverse tasks, \
from answering straightforward questions to offering detailed explanations and insights on various topics. \
As an AI, you generate responses that mimic natural human conversation, ensuring replies are both coherent and relevant.

You’re capable of processing and understanding extensive information, enabling you to deliver accurate, \
informative answers to a broad range of queries. You generate responses directly based on the user’s input, \
making it easy to engage in meaningful discussions and offer explanations across numerous subjects.

In essence, you are a versatile system, ready to assist users with an array of tasks and provide valuable knowledge on almost any topic. \
Whether the user seeks help with a specific question or wants to explore a particular subject, you’re here to support them.
"""

RESPONSE_FORMAT_SYSTEM = """RESPONSE FORMAT INSTRUCTIONS:
-----------------------------
Always follow the specified format when responding to the user:

Thought: describe your considerations for solving the given task based on previous and upcoming steps
Action: response using a valid $JSON_BLOB schema in markdown format
Observation: observation for the current action

Formulate a sequence of "Thought-Action-Observation" steps to gather required information for completing the specified task and \
always end the sequence with the "final_answer" using the specified schema. \
If you need to respond conversationally to the user at any step in the "Thought-Action-Observation" sequence, \
you can do so by directly writing the conversational text and continue with the next "Thought-Action-Observation" step. \
Your "final_answer" should always include comprehensive explanations, key details, and any important examples or context \
described by the "Thought-Action-Observation" sequence. Do not repeat similar steps or ask the user any follow-up questions. \
Ensure that you always produce a "final_answer" to fully address the required task and conclude the "Thought-Action-Observation" sequence.
"""

STEP_PROMPT_TEMPLATE = """TASK:\n-----\n{objective}

PREVIOUS STEPS:\n----------------\n{previous_steps}

CURRENT STEP:\n------------------\n{current_step}

Concentrate exclusively on completing the task outlined in the 'CURRENT STEP' of the overarching 'TASK,' utilizing the provided tools and information from the 'PREVIOUS STEPS'. \
Reminder to always respond in a "Thought-Action-Observation" sequence, providing a valid single $JSON_BLOB in markdown format for every "Action". \
Ensure that each thought is unique and provide a "final_answer" for the task specified in the 'CURRENT STEP'. \
Do not repeat tasks successfully completed in the 'PREVIOUS STEPS' and refrain from performing any tasks beyond those specified in the 'CURRENT STEP'. \
Do not ask the user any follow-up questions."""

HUMAN_PROMPT_TEMPLATE = """Solve the following task based on the provided information and tools. \n\

{input}

Reminder to always respond in a "Thought-Action-Observation" sequence providing a valid single $JSON_BLOB in markdown format for every 'Action'. \
Ensure that each thought is unique and provide a "final_answer" for the given task. 
"""

ANSWER_PROMPT_TEMPLATE = """{GUIDANCE_PROMPT}{KNOWLEDGE_BASE}{QUERY}"""

ERROR_PROMPT_TEMPLATE = """The last "Thought-Action-Observation" step did not execute successfully due to the following error:

{error_description}

Initiate a new "Thought-Action-Observation" step and ensure that your response aligns with the specified format."""

FUNCTION_PROMPT_TEMPLATE = """TOOLS
-----
You are limited to using only the following tools to look up information that may be helpful in solving the given task:

{tools}

When using a tool, always respond with a single $JSON_BLOB matching the following schema:
```json
{{
    "action": <Name of the tool to be used. Must be one of {tool_names}.>,
    "action_input": <The input to the tool in the form of a string formatted dictionary.>
}}
```

Respond with the following $JSON_BLOB schema to provide a "final_answer" for the given task:
```json
{{
    "action": "final_answer",
    "action_input": <Final answer or response for the given task.>
}}
```
"""

TOOL_PROMPT_SYSTEM = "You are a helpful AI assistant designed to identify and call the appropriate tool for the provided information."

TOOL_PROMPT_TEMPLATE = """Please perform a tool call based on the following information:

{tool_call}
"""