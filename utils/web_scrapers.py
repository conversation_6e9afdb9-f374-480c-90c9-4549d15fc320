import os, json, re, asyncio
from selenium import webdriver  
from playwright.async_api import async_playwright, <PERSON>out<PERSON>rror, <PERSON>rror as PlaywrightError
from langchain_community.document_transformers import BeautifulSoupTransformer, Html2TextTransformer
from langchain_core.documents import Document
from collections import defaultdict
from urllib.parse import quote
from httpx import Client
from parsel import Selector

headers={
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.53 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept-Language": "en-US,en;q=0.9,lt;q=0.8,et;q=0.7,de;q=0.6",
}

def parse_search_results(selector: Selector):
    """parse search results from google search page"""
    results = []
    for box in selector.xpath("//h1[contains(text(),'Search Results')]/following-sibling::div[1]/div"):
        title = box.xpath(".//h3/text()").get()
        url = box.xpath(".//h3/../@href").get()
        text = "".join(box.xpath(".//div[@data-sncf]//text()").getall())
        if not title or not url:
            continue
        # url = url.split("://")[1].replace("www.", "")
        results.append(url, title, text)
    return results

def scrape_search(query, num_results=5, num_pages=1):
    """scrape search results for a given keyword"""
    client = Client(
        headers=headers,
        follow_redirects=True,
        http2=True, 
    )
    page_list = list(range(num_pages))

    result_dict = []
    for page in page_list:
        # retrieve the SERP
        url = f"https://www.google.com/search?hl=en&q={quote(query)}" + (f"&start={10*(page-1)}" if page > 1 else "")
        print(f"scraping {query=} {page=}")
        results = defaultdict(list)
        response = client.get(url)
        assert response.status_code == 200, f"failed status_code={response.status_code}"
        # parse SERP for search result data
        selector = Selector(response.text)
        results["search"].extend(parse_search_results(selector))
        result_dict.extend(dict(results)['search'])
    return [result_dict[f][0] for f in range(min(num_results, len(result_dict)))]

async def get_h3_selector(page, query):
    await page.goto(f"https://www.google.com/search?q={quote(query)}", timeout=10000)
    await page.wait_for_load_state("networkidle")
    try:
        await page.wait_for_selector('h3', timeout=10000)
    except Exception as e:
        await page.screenshot(path=os.path.join(f"search_error.png"))
        print(f"H3 selector failed with error: {e}")
        # h3_selector = await page.wait_for_selector('h3', timeout=10000)
        # h3_selector.all()
        pattern = r"https:\/\/[^\s'\"]+"
        all_text = await page.locator("body").inner_text()
        print(all_text)
        # all_text = await page.evaluate("""
        #     () => {
        #         const body = document.body;
        #         // Remove all anchor (`<a>`) elements
        #         body.querySelectorAll('a').forEach(el => el.remove());
        #         return body.innerText; // Get text content from the body
        #     }
        # """)
        all_links = re.findall(pattern, all_text)
        print(all_links)
        await page.goto(all_links[-1], timeout=10000)
        await page.wait_for_selector('h3', timeout=10000)
    # for h_idx in range(3, 0, -1):
    #     try:
    #         print(f"Using h{h_idx} selector...")
    #         locator = await page.wait_for_selector(f'h{h_idx}', timeout=10000)
    #         locator.all()
    #     except Exception as e:
    #         print(f"h{h_idx} selector failed with error: {e}.")
    results = await page.query_selector_all('div.g')
    return results

async def playwright_search(query, num_results=10, callback_fn=None, return_results=False):
    query = re.sub(r'[^\w\s]', '', query)
    # query = re.sub(r'^[^a-zA-Z0-9]+|[^a-zA-Z0-9]+$', '', query)
    print("Playwright web scraper query:", query)
    
    search_results = []
    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(headless=True)
            # browser = await p.firefox.launch(headless=True)
            results = []
            try:
                context = await browser.new_context(user_agent=headers["User-Agent"],
                                                    extra_http_headers={
                                                        "Accept-Language": headers["Accept-Language"],
                                                        "Referer": "https://www.google.com/",
                                                        "Upgrade-Insecure-Requests": "1",
                                                    })
                page = await context.new_page()
                results = await get_h3_selector(page, query)
                print(f"Total Google search results: {len(results)}")
            except asyncio.CancelledError:
                print("Search failed to load.")
                results = []
                if callback_fn:
                        callback_fn.add_to_global_stream("Search failed to load.")
                raise TimeoutError()
            except Exception as e:
                print(f"Page failed to load with '{e}'.")
                results = []
                if callback_fn:
                    callback_fn.add_to_global_stream(str(e))
            finally:
                pass
            
            # Extract the results
            for result in results[:min(num_results, len(results))]:
                title = await result.query_selector('h3')
                link = await result.query_selector('a')
                if title and link:
                    title_text = await title.inner_text()
                    link_href = await link.get_attribute('href')
                    search_results.append({
                        "link": link_href,
                        "title": title_text,
                    })
        
        except asyncio.CancelledError:
            print("Failed to scrape google search.")
        except TimeoutError:
            print(f"Timeout error: The page took too long to load.")
        except PlaywrightError as e:
            print(f"Playwright error: {e}")
        except Exception as e:
            print(f"An error occurred: {e}")
        finally:
            # Ensure the browser is closed even if an error occurs
            await browser.close()
    if return_results:
        return search_results
    return [f["link"] for f in search_results]

async def playwright_save_url(url, filename, callback_fn=None, return_filename=False):
    html_content = None
    async with async_playwright() as p:
        try:
            browser = await p.chromium.launch(headless=True)
            # browser = await p.firefox.launch(headless=True)
            context = await browser.new_context(accept_downloads=True,
                                                user_agent=headers["User-Agent"],
                                                extra_http_headers={
                                                    "Accept-Language": headers["Accept-Language"],
                                                    "Referer": "https://www.google.com/",
                                                    "Upgrade-Insecure-Requests": "1",
                                                })
            page = await context.new_page()
            response = None
            download_obj = None
            try:
                async with page.expect_download(timeout=10000) as download_info:
                    try:
                        response = await page.goto(url, timeout=10000)
                        html_content = await page.content()
                    except Exception as e:
                        # print(f"page load failed with error: {e}")
                        pass
                    finally:
                        await page.wait_for_load_state("networkidle")
                        await page.wait_for_timeout(1000)
                download_obj = await download_info.value
            except Exception as e:
                # await page.screenshot(path=os.path.join(papers_dir, f"error_{filename}.png"))
                print(f"download thread cancelled with error: {e}")
            
            # try:
            #     response = await page.goto(url, timeout=10000)
            #     html_content = await page.content()
            #     await page.wait_for_load_state("networkidle")
            #     await page.wait_for_timeout(1000)
            # except Exception as e:
            #     # await page.screenshot(path=os.path.join(papers_dir, f"error_{filename}.png"))
            #     print(f"download thread cancelled with error: {e}")
                
            if html_content is None and download_obj is None:
                e = f"Failed to load the page. Status: {response.status if response else 'No response'}"
                print(e)
                if callback_fn:
                    callback_fn.add_to_global_stream(e)
            elif download_obj:
                html_content = None
                filename = os.path.join(filename.split(os.path.basename(filename))[0], download_obj.suggestedFilename())
                await download_obj.save_as(filename)
                e = f"Document content saved to {filename}"
                print(e)
                if callback_fn:
                    callback_fn.add_to_global_stream(e)
            else:
                html_doc = Document(page_content=html_content)
                text_loader = Html2TextTransformer()
                text_docs = text_loader.transform_documents(documents=[html_doc])
                html_content = text_docs[0].page_content
                with open(filename, "w", encoding="utf-8") as file:
                    file.write(html_content)
                e = f"HTML content saved to {filename}\n\n"
                print(e)
                if callback_fn:
                    callback_fn.add_to_global_stream(e)

        except asyncio.CancelledError:
            print("Failed to scrape website.")
        except TimeoutError:
            print(f"Timeout error: The page took too long to load.")
        except PlaywrightError as e:
            print(f"Playwright error: {e}")
        except Exception as e:
            print(f"An error occurred: {e}")
        finally:
            # Ensure the browser is closed even if an error occurs
            await browser.close()
    if html_content is None:
        return 'HTTP 404'
    if return_filename:
        return (filename, html_content)
    return html_content