#!/bin/bash

# Display help information
show_help() {
    echo "Usage: ./cli.sh [BACKEND] [FTTYPE] [additional args... [DEFAULT][--training_config][--cache_dir][optional]]"
    echo ""
    echo "Commands:"
    echo "./cli.sh unsloth sft [DEFAULT][optional args]"
    echo "./cli.sh unsloth dpo [DEFAULT][optional args]"
    echo "./cli.sh unsloth grpo [DEFAULT][optional args]"
    echo "./cli.sh axolotl sft [DEFAULT][optional args]"
    echo "./cli.sh axolotl dpo [DEFAULT][optional args]"
    echo "./cli.sh axolotl grpo [DEFAULT][optional args]"
    echo "./cli.sh dynamic sft [DEFAULT][optional args]"
    echo "./cli.sh dynamic dpo [DEFAULT][optional args]"
}

count_elements() {
    local input=$1
    if [ -z "$input" ]; then
        echo 0
        return
    fi
    IFS=',' read -ra parts <<< "$input"
    echo "${#parts[@]}"
}

int_to_list() {
    local n=$1
    local result=""
    for ((i=0; i<n; i++)); do
        result+="$i"
        if (( i < n - 1 )); then
            result+=","
        fi
    done
    echo "$result"
}

if [ "$#" -eq 0 ]; then
    show_help
    exit 1
fi

if [ "$#" -lt 2 ]; then
    echo "Error: At least two arguments are required."
    show_help
    exit 1
fi

cwd=$(pwd)
num_processes=$(count_elements $CUDA_VISIBLE_DEVICES)
gpu_count=$CUDA_VISIBLE_DEVICES
if [ "$num_processes" -eq 0 ]; then
    num_processes=$(nvidia-smi --list-gpus | wc -l)
    gpu_count=$(int_to_list $num_processes)
fi
echo "CUDA_VISIBLE_DEVICES=$gpu_count num_processes=$num_processes"
BACKEND=$1
FTTYPE=$2
shift 2

case "$FTTYPE" in
    sft|dpo|grpo)
        echo "Using $FTTYPE fine-tuning"
        ;;
    *)
        echo "Error: Invalid fine-tuning '$FTTYPE'. Allowed choices are: sft, dpo and grpo."
        exit 1
        ;;
esac

case "$BACKEND" in
    unsloth)
        case "$#" in
            0)
                echo "No arguments provided."
                mkdir -p outputs
                echo "Output notebook will be saved at ./outputs/${FTTYPE}_model_unsloth.ipynb"
                papermill "${FTTYPE}_model_unsloth.ipynb" "./outputs/${FTTYPE}_model_unsloth.ipynb" -k python3 --log-output --autosave-cell-every 1
                ;;
            *)
                echo "More than one argument provided."
                mkdir -p scripts
                jupyter nbconvert "${FTTYPE}_model_unsloth.ipynb" --output-dir='./scripts' --to python
                sed -i "/get_ipython().run_line_magic('matplotlib', 'inline')/ s/^/# /" "./scripts/${FTTYPE}_model_unsloth.py"
                cp "./scripts/${FTTYPE}_model_unsloth.py" script.py
                python script.py "$@"
                rm script.py
                ;;
        esac
        ;;
    axolotl)
        case "$#" in
            0)
                echo "No arguments provided."
                mkdir -p outputs
                echo "Output notebook will be saved at ./outputs/${FTTYPE}_model_axolotl.ipynb"
                papermill "${FTTYPE}_model_axolotl.ipynb" "./outputs/${FTTYPE}_model_axolotl.ipynb" -k python3 --log-output --autosave-cell-every 1
                ;;
            *)
                echo "More than one argument provided."
                mkdir -p scripts
                jupyter nbconvert "${FTTYPE}_model_axolotl.ipynb" --output-dir='./scripts' --to python
                sed -i "/get_ipython().run_line_magic('matplotlib', 'inline')/ s/^/# /" "./scripts/${FTTYPE}_model_axolotl.py"
                sed -i -E 's/^([[:space:]]*)!(.*)/\1print("!\2")/' "./scripts/${FTTYPE}_model_axolotl.py"
                sed -i -E 's/^([[:space:]]*)get_ipython(.*)/\1pass/' "./scripts/${FTTYPE}_model_axolotl.py"
                cp "./scripts/${FTTYPE}_model_axolotl.py" script.py
                python script.py "$@"
                rm script.py
                ;;
        esac
        ;;
    dynamic)
        case "$#" in
            0)
                echo "No arguments provided."
                case "$FTTYPE" in
                    sft|dpo)
                        CUDA_VISIBLE_DEVICES=$gpu_count accelerate launch --config_file accelerator_config.yml --num_processes=$num_processes dynamic_batching_model.py DEFAULT "--${FTTYPE}"
                        ;;
                    grpo)
                        CUDA_VISIBLE_DEVICES=$gpu_count accelerate launch --config_file accelerator_config.yml --num_processes=$num_processes dynamic_batching_model.py DEFAULT "--rl"
                        ;;
                    *)
                        echo "Error: Invalid fine-tuning '$FTTYPE'. Allowed choices are: sft, dpo, grpo."
                        exit 1
                        ;;
                esac
                ;;
            *)
                case "$FTTYPE" in
                    sft|dpo)
                        CUDA_VISIBLE_DEVICES="${gpu_count}" accelerate launch --config_file accelerator_config.yml --num_processes=$num_processes dynamic_batching_model.py "$@" "--${FTTYPE}"
                        ;;
                    grpo)
                        CUDA_VISIBLE_DEVICES="${gpu_count}" accelerate launch --config_file accelerator_config.yml --num_processes=$num_processes dynamic_batching_model.py "$@" "--rl"
                        ;;
                    *)
                        echo "Error: Invalid fine-tuning '$FTTYPE'. Allowed choices are: sft, dpo, grpo."
                        exit 1
                        ;;
                esac
                ;;
        esac
        ;;
    help)
        show_help
        ;;
    *)
        echo "Error: Unknown backend '$BACKEND'"
        show_help
        exit 1
        ;;
esac