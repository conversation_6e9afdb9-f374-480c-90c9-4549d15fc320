compute_environment: LOCAL_MACHINE
debug: false

#ZERO2
deepspeed_config:
  deepspeed_config_file: "/home/<USER>/IDSL_LLM_Docker/deepspeed_configs/zero2.json"
  zero3_init_flag: false

#ZERO3  
# deepspeed_config:
#   deepspeed_config_file: "/home/<USER>/IDSL_LLM_Docker/deepspeed_configs/zero3_bf16_cpuoffload_all.json"
#   zero3_init_flag: true

distributed_type: DEEPSPEED
downcast_bf16: true
dynamo_config:
  dynamo_backend: INDUCTOR
  dynamo_mode: max-autotune
  dynamo_use_dynamic: true
  dynamo_use_fullgraph: true
enable_cpu_affinity: false
machine_rank: 0
main_training_function: main
num_machines: 1
num_processes: 2
rdzv_backend: static
same_network: true
tpu_env: []
tpu_use_cluster: false
tpu_use_sudo: false
use_cpu: false
