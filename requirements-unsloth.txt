# Unsloth-specific requirements
# Install AFTER main requirements.txt
# Requires Python 3.12, 3.11, 3.10, or 3.9 (NOT 3.13)

# Unsloth dependencies that may conflict with main requirements
torch<=2.7.0
protobuf<4.0.0
tyro
msgspec
hf_transfer
cut_cross_entropy

# Install Unsloth
unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git

# Installation instructions:
# 1. First install main requirements: pip install -r requirements.txt
# 2. Then install Unsloth: pip install -r requirements-unsloth.txt
# 3. Or install manually: pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
