import os, sys
import collections
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict
from dataset_wrappers.wrapper import sft_dataset

class capybara_dataset(sft_dataset):
    @staticmethod
    def standardize_sharegpt(
        dataset,
        aliases_for_system    = ["system",],
        aliases_for_user      = ["user", "human", "input",],
        aliases_for_assistant = ["gpt", "assistant", "output",],
    ):
        aliases_mapping = {}
        for x in aliases_for_system:    aliases_mapping[x] = "system"
        for x in aliases_for_user:      aliases_mapping[x] = "user"
        for x in aliases_for_assistant: aliases_mapping[x] = "assistant"
        
        def _standardize_dataset(examples):
            convos = examples["conversation"]
            all_convos = []
            for convo in convos:
                new_convo = [
                    f for tup in convo for f in [{ "role" : aliases_mapping['input'], "content" : tup['input'], },
                                                { "role" : aliases_mapping['output'], "content" : tup['output'], }]
                ]
                all_convos.append(new_convo)
            pass
            return { "conversations" : all_convos, }
        pass

        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', *args, **kwargs):
        super(capybara_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, *args, **kwargs)
        if test_size>0.:
            self.iterable_dataset_traineval = self.iterable_dataset['train'].train_test_split(test_size=test_size)
            self.iterable_dataset_traineval['train'] = self.standardize_sharegpt(self.iterable_dataset_traineval['train'])
            self.iterable_dataset_traineval['test'] = self.standardize_sharegpt(self.iterable_dataset_traineval['test'])
        else:
            self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset['train'])})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
    
    def __len__(self):
        return self.iterable_dataset_traineval[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset_traineval[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True)
            for split_name in self.iterable_dataset_traineval.column_names:
                self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["conversations", "text"]])
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None