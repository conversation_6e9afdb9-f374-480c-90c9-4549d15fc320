import os, sys
from os import listdir
from os.path import isfile, isdir, join
from typing import Literal

import abc
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict

class VirtualException(BaseException):
    def __init__(self, _type, _func):
        BaseException(self)

class sft_dataset(metaclass = abc.ABCMeta):
    def __init__(self, dataset_name, cache_dir='./', force_download=False, download_split=None, *args, **kwargs):
        super(sft_dataset, self).__init__(*args, **kwargs)
        self.cache_dir = join(cache_dir, dataset_name.replace('/', '--') + (f"--{download_split}" if download_split else ""))
        self.tokenizer = None
        self.iterable_dataset_traineval = None
        
        if not isdir(self.cache_dir) or force_download:
            print(f'Downloading dataset: {dataset_name}')
            self.iterable_dataset = load_dataset(
                dataset_name, download_split,
                cache_dir=self.cache_dir,
                trust_remote_code=True
            )
        else:
            print(f'Loading dataset: {self.cache_dir}')
            self.iterable_dataset = load_dataset(
                self.cache_dir,
                trust_remote_code=True
            )
            
    def formatting_prompts_func(self, examples):
        assert self.tokenizer
        convos = examples["conversations"]
        texts = [self.tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False) for convo in convos]
        return { "text" : texts, }
    
    @abc.abstractmethod
    def format_dataset(self, tokenizer, chat_template):
        pass
    
    @abc.abstractmethod
    def __len__(self):
        pass
    
    @abc.abstractmethod
    def __getitem__(self, idx):
        pass

class dpo_dataset(metaclass = abc.ABCMeta):
    def __init__(self, dataset_name, cache_dir='./', force_download=False, download_split=None, *args, **kwargs):
        super(dpo_dataset, self).__init__(*args, **kwargs)
        self.cache_dir = join(cache_dir, dataset_name.replace('/', '--') + (f"--{download_split}" if download_split else ""))
        self.tokenizer = None
        self.chat_headers = None
        self.iterable_dataset_traineval = None
        
        if not isdir(self.cache_dir) or force_download:
            print(f'Downloading dataset: {dataset_name}')
            self.iterable_dataset = load_dataset(
                dataset_name, download_split,
                cache_dir=self.cache_dir,
                trust_remote_code=True
            )
        else:
            print(f'Loading dataset: {self.cache_dir}')
            self.iterable_dataset = load_dataset(
                self.cache_dir,
                trust_remote_code=True
            )
            
    def formatting_prompts_func(self, examples, system_header, user_header, assistant_header):
        assert self.tokenizer
        prompt_keys = ["prompt", "chosen", "rejected"]
        texts = {}
        for prompt_key in prompt_keys:
            convos = examples[prompt_key]
            if prompt_key=="prompt":
                texts[prompt_key] = [self.tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = True) for convo in convos]
            elif prompt_key in ["chosen", "rejected"] and assistant_header is not None:
                texts[prompt_key] = [self.tokenizer.apply_chat_template(convo, tokenize = False).split(assistant_header)[-1] for convo in convos]
            else:
                texts[prompt_key] = [self.tokenizer.apply_chat_template(convo, tokenize = False) for convo in convos]
        return texts
    
    @abc.abstractmethod
    def format_dataset(self, tokenizer, chat_template, chat_headers):
        pass
    
    @abc.abstractmethod
    def __len__(self):
        pass
    
    @abc.abstractmethod
    def __getitem__(self, idx):
        pass
    
class grpo_dataset(metaclass = abc.ABCMeta):
    def __init__(self, dataset_name, cache_dir='./', force_download=False, download_split=None, *args, **kwargs):
        super(grpo_dataset, self).__init__(*args, **kwargs)
        self.cache_dir = join(cache_dir, dataset_name.replace('/', '--') + (f"--{download_split}" if download_split else ""))
        self.tokenizer = None
        self.reward_model = None
        self.iterable_dataset_traineval = None
        
        if not isdir(self.cache_dir) or force_download:
            print(f'Downloading dataset: {dataset_name}')
            self.iterable_dataset = load_dataset(
                dataset_name, download_split,
                cache_dir=self.cache_dir,
                trust_remote_code=True
            )
        else:
            print(f'Loading dataset: {self.cache_dir}')
            self.iterable_dataset = load_dataset(
                self.cache_dir,
                trust_remote_code=True
            )
    
    def formatting_prompts_func(self, examples, system_header, user_header, assistant_header, ft_type: Literal['sft', 'dpo']='sft'):
        assert self.tokenizer
        if ft_type=='dpo':
            prompt_keys = ["prompt", "chosen", "rejected"]
            texts = {}
            for prompt_key in prompt_keys:
                convos = examples[prompt_key]
                if prompt_key=="prompt":
                    texts[prompt_key] = [self.tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = True) for convo in convos]
                elif prompt_key in ["chosen", "rejected"] and assistant_header is not None:
                    texts[prompt_key] = [self.tokenizer.apply_chat_template(convo, tokenize = False).split(assistant_header)[-1] for convo in convos]
                else:
                    texts[prompt_key] = [self.tokenizer.apply_chat_template(convo, tokenize = False) for convo in convos]
            return texts
        else:
            convos = examples["conversations"]
            texts = [self.tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False) for convo in convos]
            return { "text" : texts, }
    
    def formatting_response_func(self, examples):
        assert self.tokenizer
        assert self.reward_model
        convos = examples["conversations"]
        prompts = []
        thoughts = []
        answers = []
        for convo in convos:
            new_convo = {
                'prompt': convo[:-1],
                'thought': self.reward_model.extract_xml_thought(convo[-1]['content']),
                'answer': self.reward_model.extract_xml_answer(convo[-1]['content']),
            }
            prompts.append(new_convo['prompt'])
            thoughts.append(new_convo['thought'])
            answers.append(new_convo['answer'])
        return {'prompt': prompts, 'thought': thoughts, 'answer': answers}
    
    @abc.abstractmethod
    def format_rewards(self, response_template, rewards_fn, rewards_fn_args):
        pass
            
    @abc.abstractmethod
    def format_dataset(self, tokenizer, chat_template):
        pass
    
    @abc.abstractmethod
    def __len__(self):
        pass
    
    @abc.abstractmethod
    def __getitem__(self, idx):
        pass