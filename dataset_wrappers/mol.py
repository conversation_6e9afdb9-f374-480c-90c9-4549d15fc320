import os, sys
import collections, re
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta
from typing import Literal

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict
from dataset_wrappers.wrapper import grpo_dataset
from dataset_wrappers.rewards import reward_class
from datasets import disable_caching, enable_caching

XML_COT_FORMAT = "<think>\n...\n</think>\n<answer>\n...\n</answer>"
GRPO_SYSTEM_PROMPT = (
    "You are an advanced language model capable of performing detailed internal reasoning.\n\n"
    "For every task or query, apply a chain-of-thought process to logically analyze and decompose the problem "
    "into clear, systematic steps. After completing your internal reasoning, provide a final answer that is "
    "accurate, concise, and directly addresses the task.\n\n"
    "Enclose your reasoning within <think> and </think> tags, "
    "and place the final answer within <answer> and </answer> tags. "
    "Respond using the following XML format:\n\n"
    f"{XML_COT_FORMAT}"
)

class mol_dataset(grpo_dataset):
    @staticmethod
    def standardize_sharegpt(
        dataset,
        aliases_for_system    = ["system",],
        aliases_for_user      = ["user", "human", "input",],
        aliases_for_assistant = ["gpt", "assistant", "output",],
        system_prompt = None,
        response_format = None,
    ):
        # Mapping for aliases
        aliases_mapping = {}
        for x in aliases_for_system:    aliases_mapping[x] = "system"
        for x in aliases_for_user:      aliases_mapping[x] = "user"
        for x in aliases_for_assistant: aliases_mapping[x] = "assistant"
        if response_format is not None:
            response_format = response_format.replace('...', '{}')

        def _standardize_dataset(examples):
            instructions = examples['instruction']
            inputs = examples['input']
            outputs = examples['output']
            all_convos = []
            
            for idx in range(len(instructions)):
                if response_format is not None and system_prompt is not None:
                    all_convos.append([
                        {'role': 'system', 'content': '{}'.format(system_prompt)},
                        {'role': 'user', 'content': 'TASK: {}\n\n{}'.format(instructions[idx], inputs[idx])},
                        {'role': 'assistant', 'content': response_format.format('...', outputs[idx])},
                    ])
                else:
                    all_convos.append([
                        {'role': 'system', 'content': instructions[idx]},
                        {'role': 'user', 'content': inputs[idx]},
                        {'role': 'assistant', 'content': outputs[idx]},
                    ])
            return { "conversations" : all_convos, }
        
        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', subset_name='Molecule-oriented Instructions', system_prompt=None, response_template=None, *args, **kwargs):
        super(mol_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, download_split=subset_name, *args, **kwargs)
        
        self.system_prompt = system_prompt if system_prompt else GRPO_SYSTEM_PROMPT
        self.response_template = response_template if response_template else XML_COT_FORMAT
        self.system_prompt = self.system_prompt.format(self.response_template)
        
        tags = re.findall(r'<[^>]+>', self.response_template)
        print("System prompt:", self.system_prompt)
        print("Response tags:", tags)
        thought_start_tag = tags[0]
        thought_end_tag = tags[1]
        answer_start_tag = tags[2]
        answer_end_tag = tags[3]
        self.reward_model = reward_class(thought_start_tag, thought_end_tag, answer_start_tag, answer_end_tag)
        
        disable_caching()
        try:
            self.iterable_dataset_traineval = DatasetDict({'train': self.iterable_dataset[f] for f in [
                'description_guided_molecule_design',
                # 'forward_reaction_prediction',
                # 'reagent_prediction',
                # 'retrosynthesis',
            ]})
        except Exception as e:
            print(f"Issue with dataset cache: {e}. Please delete '{self.cache_dir}'")
            self.iterable_dataset_traineval = DatasetDict({'train': concatenate_datasets([self.iterable_dataset[f] for f in self.iterable_dataset.column_names])})
        
        if test_size>0.:
            self.iterable_dataset_traineval = self.iterable_dataset_traineval['train'].train_test_split(test_size=test_size)
            self.iterable_dataset_traineval['train'] = self.standardize_sharegpt(self.iterable_dataset_traineval['train'], 
                                                                                system_prompt=self.system_prompt, response_format=self.response_template)
            self.iterable_dataset_traineval['test'] = self.standardize_sharegpt(self.iterable_dataset_traineval['test'],
                                                                                system_prompt=self.system_prompt, response_format=self.response_template)
        else:
            self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset_traineval['train'],
                                                                                system_prompt=self.system_prompt, response_format=self.response_template)})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
    
    def __len__(self):
        return self.iterable_dataset_traineval[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset_traineval[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template, chat_headers={"system_header":None, "user_header":None, "assistant_header":None}, ft_type: Literal['sft', 'dpo', 'rl']='sft'):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            
            if ft_type=='sft':
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.standardize_sharegpt(self.iterable_dataset_traineval[split_name])
                self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True,
                                                                                                                fn_kwargs={
                                                                                                                    'ft_type': 'sft',
                                                                                                                    'system_header': chat_headers['system_header'],
                                                                                                                    'user_header': chat_headers['user_header'],
                                                                                                                    'assistant_header': chat_headers['assistant_header'],
                                                                                                                    })
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["conversations", "text"]])
                return self.tokenizer
            elif ft_type=='dpo':
                raise ValueError(f"{ft_type} not implemented.")
            elif ft_type=='rl':
                self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_response_func, batched = True)
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["prompt", "thought", "answer"]])
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None
    
    def format_rewards(self, rewards_fn):
        assert self.reward_model
        reward_fn_list = [getattr(self.reward_model, f) for f in rewards_fn]
        assert len(reward_fn_list)>0
        return reward_fn_list