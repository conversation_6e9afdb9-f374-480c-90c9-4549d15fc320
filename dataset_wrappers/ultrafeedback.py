import os, sys
import collections
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict
from dataset_wrappers.wrapper import dpo_dataset

class ultrafeedback_dataset(dpo_dataset):
    @staticmethod
    def standardize_feedback(
        dataset,
        aliases_for_system    = ["system",],
        aliases_for_user      = ["user", "human", "input",],
        aliases_for_assistant = ["gpt", "assistant", "output",],
    ):
        role_key    = 'role'
        content_key = 'content'
        convos = dataset[:10]["chosen"]
        uniques = collections.defaultdict(list)
        for convo in convos:
            for message in convo:
                for key, value in message.items():
                    if key not in [role_key, content_key]:
                        continue
                    uniques[key].append(value)
        pass

        # Must be only 2 entries
        assert(len(uniques.keys()) == 2)

        # Check roles are in aliases
        all_aliases = set(aliases_for_system + aliases_for_user + aliases_for_assistant)
        roles = set(uniques[role_key])
        leftover_aliases = (all_aliases | roles) - all_aliases
        if len(leftover_aliases) != 0:
            raise TypeError(
                f"Unsloth: {list(leftover_aliases)} are not in aliases. Please update aliases."
            )
        pass

        # Mapping for aliases
        aliases_mapping = {}
        for x in aliases_for_system:    aliases_mapping[x] = "system"
        for x in aliases_for_user:      aliases_mapping[x] = "user"
        for x in aliases_for_assistant: aliases_mapping[x] = "assistant"

        def _standardize_dataset(examples):
            convos = examples["chosen"]
            chosen_convos = []
            for convo in convos:
                new_convo = [
                    { "role" : aliases_mapping[message[role_key]], "content" : message[content_key], }
                    for message in convo[-1:]
                ]
                chosen_convos.append(new_convo)
            
            convos = examples["rejected"]
            rejected_convos = []
            for convo in convos:
                new_convo = [
                    { "role" : aliases_mapping[message[role_key]], "content" : message[content_key], }
                    for message in convo[-1:]
                ]
                rejected_convos.append(new_convo)
            
            prompts = examples["chosen"]
            all_prompts = []
            for prompt in prompts:
                new_prompt = [
                    { "role" : aliases_mapping[message[role_key]], "content" : message[content_key], }
                    for message in prompt[:-1]
                ]
                all_prompts.append(new_prompt)
            return {"prompt": all_prompts, "chosen" : chosen_convos, "rejected": rejected_convos}
        pass

        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', *args, **kwargs):
        super(ultrafeedback_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, *args, **kwargs)
        if test_size>0.:
            self.iterable_dataset_traineval = self.iterable_dataset['train'].train_test_split(test_size=test_size)
            self.iterable_dataset_traineval['train'] = self.standardize_feedback(self.iterable_dataset_traineval['train'])
            self.iterable_dataset_traineval['test'] = self.standardize_feedback(self.iterable_dataset_traineval['test'])
        else:
            self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_feedback(self.iterable_dataset['train'])})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
        
    def __len__(self):
        return self.iterable_dataset_traineval[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset_traineval[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template, chat_headers={"system_header":None, "user_header":None, "assistant_header":None}):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            self.chat_headers = chat_headers
            self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True, fn_kwargs=chat_headers)
            for split_name in self.iterable_dataset_traineval.column_names:
                self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["prompt", "chosen", "rejected"]])
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None