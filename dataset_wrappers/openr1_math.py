import os, sys
import re, collections
import numpy as np
import multiprocessing
from os import listdir
from os.path import isfile, isdir, join
from tqdm.notebook import tqdm
from abc import ABCMeta
from typing import Literal

from unsloth.chat_templates import get_chat_template
from torch.utils.data import DataLoader
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict, Dataset
from dataset_wrappers.wrapper import grpo_dataset
from dataset_wrappers.rewards import reward_class

XML_COT_FORMAT = "<think>\n...\n</think>\n<answer>\n...\n</answer>"
GRPO_SYSTEM_PROMPT = "You are a helpful AI Assistant that provides well-reasoned and detailed responses. \n\n\
You first think about the reasoning process as an internal monologue and then provide the user with the answer. Respond in the following format: \n{}"

class openr1_math_dataset(grpo_dataset):
    @staticmethod
    def standardize_sharegpt(
        dataset,
        aliases_for_system    = ["system",],
        aliases_for_user      = ["user", "human", "input",],
        aliases_for_assistant = ["gpt", "assistant", "output",],
        system_prompt = GRPO_SYSTEM_PROMPT,
    ):
        convos = dataset[:10]["messages"]
        uniques = collections.defaultdict(list)
        for convo in convos:
            for message in convo:
                for key, value in message.items():
                    if key not in ['from', 'value', 'role', 'content']:
                        continue
                    uniques[key].append(value)
        pass

        # Must be only 2 entries
        assert(len(uniques.keys()) == 2)

        keys = list(uniques.keys())
        length_first  = len(set(uniques[keys[0]]))
        length_second = len(set(uniques[keys[1]]))

        # Role is assigned to the first element
        if length_first < length_second:
            role_key    = keys[0]
            content_key = keys[1]
        else:
            role_key    = keys[1]
            content_key = keys[0]
        pass

        # Check roles are in aliases
        all_aliases = set(aliases_for_system + aliases_for_user + aliases_for_assistant)
        roles = set(uniques[role_key])
        leftover_aliases = (all_aliases | roles) - all_aliases
        if len(leftover_aliases) != 0:
            raise TypeError(
                f"Unsloth: {list(leftover_aliases)} are not in aliases. Please update aliases."
            )
        pass

        # Mapping for aliases
        aliases_mapping = {}
        for x in aliases_for_system:    aliases_mapping[x] = "system"
        for x in aliases_for_user:      aliases_mapping[x] = "user"
        for x in aliases_for_assistant: aliases_mapping[x] = "assistant"

        def _standardize_dataset(examples):
            convos = examples["messages"]
            all_convos = []
            for convo in convos:
                if aliases_mapping[convo[0][role_key]]!="system":
                    convo.insert(0, {role_key: "system", content_key: system_prompt})
                new_convo = [
                    { "role" : aliases_mapping[message[role_key]], "content" : message[content_key] + ('\n\n{}'.format(system_prompt.split("\n\n")[-1])\
                        if (aliases_mapping[message[role_key]]=="system" and message[content_key]!=system_prompt) else ""), }
                    for message in convo
                ]
                all_convos.append(new_convo)
            return { "conversations" : all_convos, }
        
        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    @staticmethod
    def standardize_feedback(
        dataset,
        thought_start_tag = "<think>",
        thought_end_tag = "</think>",
        answer_start_tag = "<answer>",
        answer_end_tag = "</answer>",
        system_prompt = GRPO_SYSTEM_PROMPT,
    ):
        dpo_dataset = Dataset.from_dict({"prompt": [], "chosen" : [], "rejected": []})
        for idx in tqdm(range(0, dataset.num_rows, 1000)):
            examples = dataset[idx:min(idx+1000, dataset.num_rows)]
            problems = examples["problem"]
            generations = examples["generations"]
            correctness = examples["correctness_math_verify"]
            completeness = examples["is_reasoning_complete"]
            
            all_prompts = []
            chosen_convos = []
            rejected_convos = []
            for idx in range(len(problems)):
                problem = problems[idx]
                generation = generations[idx]
                generation_quality = np.array([(cor and com) for cor, com in zip(correctness[idx], completeness[idx])])
                true_indices = np.where(generation_quality)[0]
                false_indices = np.where(np.logical_not(generation_quality))[0]
                if true_indices.shape[0]>0 and false_indices.shape[0]>0:
                    dpo_pairs = np.stack(np.meshgrid(true_indices, false_indices)).reshape(-1, 2)
                    for pair_id in range(dpo_pairs.shape[0]):
                        prompt = [
                            {'role': 'system', 'content': system_prompt,},
                            {'role': 'user', 'content': problem,},
                        ]
                        chosen = [
                            {'role': 'assistant', 'content': generation[dpo_pairs[pair_id, 0]],}
                        ]
                        rejected = [
                            {'role': 'assistant', 'content': generation[dpo_pairs[pair_id, 1]],}
                        ]
                        all_prompts.append(prompt)
                        chosen_convos.append(chosen)
                        rejected_convos.append(rejected)
                else:
                    continue
            if len(all_prompts)>0:
                new_dataset = Dataset.from_dict({"prompt": all_prompts, "chosen" : chosen_convos, "rejected": rejected_convos})
                dpo_dataset = concatenate_datasets([dpo_dataset, new_dataset])
        return dpo_dataset
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', subset_name='default', system_prompt=None, response_template=None, *args, **kwargs):
        super(openr1_math_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, download_split=subset_name, *args, **kwargs)
        self.system_prompt = system_prompt if system_prompt else GRPO_SYSTEM_PROMPT
        self.response_template = response_template if response_template else XML_COT_FORMAT
        self.system_prompt = self.system_prompt.format(self.response_template)
        
        tags = re.findall(r'<[^>]+>', self.response_template)
        print("System prompt:", self.system_prompt)
        print("Response tags:", tags)
        thought_start_tag = tags[0]
        thought_end_tag = tags[1]
        answer_start_tag = tags[2]
        answer_end_tag = tags[3]
        self.reward_model = reward_class(thought_start_tag, thought_end_tag, answer_start_tag, answer_end_tag)
        
        print(self.iterable_dataset)
        if test_size>0.:
            self.iterable_dataset_traineval = self.iterable_dataset['train'].train_test_split(test_size=test_size)
            self.iterable_dataset_traineval['train'] = self.standardize_sharegpt(self.iterable_dataset_traineval['train'], system_prompt=self.system_prompt)
            self.iterable_dataset_traineval['test'] = self.standardize_sharegpt(self.iterable_dataset_traineval['test'], system_prompt=self.system_prompt)
        else:
            self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset['train'], system_prompt=self.system_prompt)})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
        
    def __len__(self):
        return self.iterable_dataset[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template, chat_headers={"system_header":None, "user_header":None, "assistant_header":None}, ft_type: Literal['sft', 'dpo', 'rl']='sft'):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            
            if ft_type=='sft':
                self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True,
                                                                                                                fn_kwargs={
                                                                                                                    'ft_type': 'sft',
                                                                                                                    'system_header': chat_headers['system_header'],
                                                                                                                    'user_header': chat_headers['user_header'],
                                                                                                                    'assistant_header': chat_headers['assistant_header'],
                                                                                                                    })
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["conversations", "text"]])
            elif ft_type=='dpo':
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.standardize_feedback(self.iterable_dataset_traineval[split_name], system_prompt=self.system_prompt)
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].map(self.formatting_prompts_func, batched = True, 
                                                                                                                fn_kwargs={
                                                                                                                    'ft_type': 'dpo',
                                                                                                                    'system_header': chat_headers['system_header'],
                                                                                                                    'user_header': chat_headers['user_header'],
                                                                                                                    'assistant_header': chat_headers['assistant_header'],
                                                                                                                    })
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["prompt", "chosen", "rejected"]])
            elif ft_type=='rl':
                self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_response_func, batched = True)
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["prompt", "thought", "answer"]])
            
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None
    
    def format_rewards(self, rewards_fn):
        assert self.reward_model
        reward_fn_list = [getattr(self.reward_model, f) for f in rewards_fn]
        assert len(reward_fn_list)>0
        return reward_fn_list