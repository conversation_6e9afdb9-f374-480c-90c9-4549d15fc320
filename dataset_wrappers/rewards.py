import asyncio
import json
import math
import re, ast, astor
import inspect
import difflib
import torch
from typing import Dict, Literal

from latex2sympy2_extended import NormalizationConfig
from math_verify import LatexExtractionConfig, parse, verify
from transformers.utils.import_utils import _is_package_available
from sentence_transformers import SentenceTransformer

# Use same as transformers.utils.import_utils
device = torch.device('cuda')
_e2b_available = _is_package_available("e2b")
mol_model = SentenceTransformer("gbyuvd/ChemEmbed-v01", device=device)
mol_sim_threshold = 0.8
answer_len_ratio_threshold = 0.6

def is_e2b_available() -> bool:
    return _e2b_available

if is_e2b_available():
    from dotenv import load_dotenv
    from e2b_code_interpreter import AsyncSandbox

    load_dotenv()
else:
    AsyncSandbox = None
    
async def run_script(sbx: AsyncSandbox, script: str, language: str) -> float:
    execution = await sbx.run_code(script, language=language)
    try:
        return float(execution.text)
    except (TypeError, ValueError):
        return 0.0

def run_async_from_sync(scripts: list[str], language: str) -> list[float]:
    """Function wrapping the `run_async` function."""
    # Create a new event loop and set it
    try:
        # Run the async function and get the result
        rewards = asyncio.run(run_async(scripts, language))
    except Exception as e:
        print(f"Error from E2B executor async: {e}")
        raise e

    return rewards

async def run_async(scripts: list[str], language: str) -> list[float]:
    # Create the sandbox by hand, currently there's no context manager for this version
    sbx = await AsyncSandbox.create(timeout=30, request_timeout=3)

    # Create a list of tasks for running scripts concurrently
    tasks = [run_script(sbx, script, language) for script in scripts]

    # Wait for all tasks to complete and gather their results as they finish
    results = await asyncio.gather(*tasks)
    rewards = list(results)  # collect results

    # Kill the sandbox after all the tasks are complete
    await sbx.kill()

    return rewards

def insert_static_var_string(source_code, static_var_string, k: int=-1):
    class FuncTransformer(ast.NodeTransformer):
        def visit_FunctionDef(self, node):
            self.generic_visit(node)
            if k < len(node.args.args) and k > -1:
                del node.args.args[k]
            static_lines = ast.parse(static_var_string).body[:]
            for idx, static_line in enumerate(static_lines):
                node.body.insert(idx, static_line)
            return node

    tree = ast.parse(source_code)
    transformer = FuncTransformer()
    modified_tree = transformer.visit(tree)
    return astor.to_source(modified_tree)

class reward_class:
    
    @staticmethod
    def accuracy_reward(completions, solution, **kwargs):
        """Reward function that checks if the completion is the same as the ground truth."""
        contents = [completion[0]["content"] for completion in completions]
        rewards = []
        for content, sol in zip(contents, solution):
            gold_parsed = parse(
                sol,
                extraction_mode="first_match",
                extraction_config=[LatexExtractionConfig()],
            )
            if len(gold_parsed) != 0:
                # We require the answer to be provided in correct latex (no malformed operators)
                answer_parsed = parse(
                    content,
                    extraction_config=[
                        LatexExtractionConfig(
                            normalization_config=NormalizationConfig(
                                nits=False,
                                malformed_operators=False,
                                basic_latex=True,
                                equations=True,
                                boxed="all",
                                units=True,
                            ),
                            # Ensures that boxed is tried first
                            boxed_match_priority=0,
                            try_extract_without_anchor=False,
                        )
                    ],
                    extraction_mode="first_match",
                )
                # Reward 1 if the content is the same as the ground truth, 0 otherwise
                try:
                    reward = float(verify(answer_parsed, gold_parsed))
                except Exception as e:
                    print(f"verify failed: {e}, answer: {answer_parsed}, gold: {gold_parsed}")
                    reward = 0.0
            else:
                # If the gold solution is not parseable, we reward 1 to skip this example
                reward = 1.0
                print("Failed to parse gold solution: ", sol)
            rewards.append(reward)

        return rewards

    @staticmethod
    def reasoning_steps_reward(completions, **kwargs):
        r"""Reward function that checks for clear step-by-step reasoning.
        Regex pattern:
            Step \d+: - matches "Step 1:", "Step 2:", etc.
            ^\d+\. - matches numbered lists like "1.", "2.", etc. at start of line
            \n- - matches bullet points with hyphens
            \n\* - matches bullet points with asterisks
            First,|Second,|Next,|Finally, - matches transition words
        """
        pattern = r"(Step \d+:|^\d+\.|\n-|\n\*|First,|Second,|Next,|Finally,)"
        completion_contents = [completion[0]["content"] for completion in completions]
        matches = [len(re.findall(pattern, content)) for content in completion_contents]

        # Magic number 3 to encourage 3 steps and more, otherwise partial reward
        return [min(1.0, count / 3) for count in matches]

    @staticmethod
    def len_reward(completions: list[Dict[str, str]], solution: list[str], **kwargs) -> float:
        """Compute length-based rewards to discourage overthinking and promote token efficiency.

        Taken from the Kimi 1.5 tech report: https://arxiv.org/abs/2501.12599

        Args:
            completions: List of model completions
            solution: List of ground truth solutions

        Returns:
            List of rewards where:
            - For correct answers: reward = 0.5 - (len - min_len)/(max_len - min_len)
            - For incorrect answers: reward = min(0, 0.5 - (len - min_len)/(max_len - min_len))
        """
        contents = [completion[0]["content"] for completion in completions]

        # First check correctness of answers
        correctness = []
        for content, sol in zip(contents, solution):
            gold_parsed = parse(
                sol,
                extraction_mode="first_match",
                extraction_config=[LatexExtractionConfig()],
            )
            if len(gold_parsed) == 0:
                # Skip unparseable examples
                correctness.append(True)  # Treat as correct to avoid penalizing
                print("Failed to parse gold solution: ", sol)
                continue

            answer_parsed = parse(
                content,
                extraction_config=[
                    LatexExtractionConfig(
                        normalization_config=NormalizationConfig(
                            nits=False,
                            malformed_operators=False,
                            basic_latex=True,
                            equations=True,
                            boxed=True,
                            units=True,
                        ),
                        boxed_match_priority=0,
                        try_extract_without_anchor=False,
                    )
                ],
                extraction_mode="first_match",
            )
            correctness.append(verify(answer_parsed, gold_parsed))

        # Calculate lengths
        lengths = [len(content) for content in contents]
        min_len = min(lengths)
        max_len = max(lengths)

        # If all responses have the same length, return zero rewards
        if max_len == min_len:
            return [0.0] * len(completions)

        rewards = []
        for length, is_correct in zip(lengths, correctness):
            lambda_val = 0.5 - (length - min_len) / (max_len - min_len)

            if is_correct:
                reward = lambda_val
            else:
                reward = min(0, lambda_val)

            rewards.append(float(reward))

        return rewards


    @staticmethod
    def get_cosine_scaled_reward(
        min_value_wrong: float = -1.0,
        max_value_wrong: float = -0.5,
        min_value_correct: float = 0.5,
        max_value_correct: float = 1.0,
        max_len: int = 1000,
    ):
        def cosine_scaled_reward(completions, solution, **kwargs):
            """Reward function that scales based on completion length using a cosine schedule.

            Shorter correct solutions are rewarded more than longer ones.
            Longer incorrect solutions are penalized less than shorter ones.

            Args:
                completions: List of model completions
                solution: List of ground truth solutions

            This function is parameterized by the following arguments:
                min_value_wrong: Minimum reward for wrong answers
                max_value_wrong: Maximum reward for wrong answers
                min_value_correct: Minimum reward for correct answers
                max_value_correct: Maximum reward for correct answers
                max_len: Maximum length for scaling
            """
            contents = [completion[0]["content"] for completion in completions]
            rewards = []

            for content, sol in zip(contents, solution):
                gold_parsed = parse(sol, extraction_mode="first_match", extraction_config=[LatexExtractionConfig()])
                if len(gold_parsed) == 0:
                    rewards.append(1.0)  # Skip unparseable examples
                    print("Failed to parse gold solution: ", sol)
                    continue

                answer_parsed = parse(
                    content,
                    extraction_config=[
                        LatexExtractionConfig(
                            normalization_config=NormalizationConfig(
                                nits=False,
                                malformed_operators=False,
                                basic_latex=True,
                                equations=True,
                                boxed=True,
                                units=True,
                            ),
                            boxed_match_priority=0,
                            try_extract_without_anchor=False,
                        )
                    ],
                    extraction_mode="first_match",
                )

                is_correct = verify(answer_parsed, gold_parsed)
                gen_len = len(content)

                # Apply cosine scaling based on length
                progress = gen_len / max_len
                cosine = math.cos(progress * math.pi)

                if is_correct:
                    min_value = min_value_correct
                    max_value = max_value_correct
                else:
                    # Swap min/max for incorrect answers
                    min_value = max_value_wrong
                    max_value = min_value_wrong

                reward = min_value + 0.5 * (max_value - min_value) * (1.0 + cosine)
                rewards.append(float(reward))

            return rewards

        return cosine_scaled_reward

    @staticmethod
    def get_repetition_penalty_reward(ngram_size: int, max_penalty: float):
        """
        Computes N-gram repetition penalty as described in Appendix C.2 of https://arxiv.org/abs/2502.03373.
        Reference implementation from: https://github.com/eddycmu/demystify-long-cot/blob/release/openrlhf/openrlhf/reward/repetition.py

        Args:
        ngram_size: size of the n-grams
        max_penalty: Maximum (negative) penalty for wrong answers
        """
        if max_penalty > 0:
            raise ValueError(f"max_penalty {max_penalty} should not be positive")

        def zipngram(text: str, ngram_size: int):
            words = text.lower().split()
            return zip(*[words[i:] for i in range(ngram_size)])

        def repetition_penalty_reward(completions, **kwargs) -> float:
            """
            reward function the penalizes repetitions
            ref implementation: https://github.com/eddycmu/demystify-long-cot/blob/release/openrlhf/openrlhf/reward/repetition.py

            Args:
                completions: List of model completions
            """

            contents = [completion[0]["content"] for completion in completions]
            rewards = []
            for completion in contents:
                if completion == "":
                    rewards.append(0.0)
                    continue
                if len(completion.split()) < ngram_size:
                    rewards.append(0.0)
                    continue

                ngrams = set()
                total = 0
                for ng in zipngram(completion, ngram_size):
                    ngrams.add(ng)
                    total += 1

                scaling = 1 - len(ngrams) / total
                reward = scaling * max_penalty
                rewards.append(reward)
            return rewards

        return repetition_penalty_reward

    @staticmethod
    def code_reward(completions, **kwargs) -> list[float]:
        """Reward function that evaluates code snippets using the E2B code interpreter.

        Assumes the dataset contains a `verification_info` column with test cases.
        """
        if not is_e2b_available():
            raise ImportError(
                "E2B is not available and required for this reward function. Please install E2B with "
                "`pip install e2b-code-interpreter` and add an API key to a `.env` file."
            )

        # TODO: add support for other languages in E2B: https://e2b.dev/docs/code-interpreting/supported-languages
        """Returns a reward function that evaluates code snippets in a sandbox."""
        
        def extract_code(completion: str) -> str:
            pattern = re.compile(r"```python\n(.*?)```", re.DOTALL)
            matches = pattern.findall(completion)
            extracted_answer = matches[-1] if len(matches) >= 1 else ""
            return extracted_answer

        evaluation_script_template = """
        import subprocess
        import json

        def evaluate_code(code, test_cases):
            passed = 0
            total = len(test_cases)
            exec_timeout = 5

            for case in test_cases:
                process = subprocess.run(
                    ["python3", "-c", code],
                    input=case["input"],
                    text=True,
                    capture_output=True,
                    timeout=exec_timeout
                )

                if process.returncode != 0:  # Error in execution
                    continue

                output = process.stdout.strip()

                # TODO: implement a proper validator to compare against ground truth. For now we just check for exact string match on each line of stdout.
                all_correct = True
                for line1, line2 in zip(output.split('\\n'), case['output'].split('\\n')):
                    all_correct = all_correct and line1.strip() == line2.strip()

                if all_correct:
                    passed += 1

            success_rate = (passed / total)
            return success_rate

        code_snippet = {code}
        test_cases = json.loads({test_cases})

        evaluate_code(code_snippet, test_cases)
        """
        code_snippets = [extract_code(completion[-1]["content"]) for completion in completions]
        verification_info = kwargs["verification_info"]
        scripts = [
            evaluation_script_template.format(code=json.dumps(code), test_cases=json.dumps(json.dumps(info["test_cases"])))
            for code, info in zip(code_snippets, verification_info)
        ]

        language = verification_info[0]["language"]

        if not all(v["language"] == language for v in verification_info):
            raise ValueError("All verification_info must have the same language", verification_info)
        try:
            rewards = run_async_from_sync(scripts, language)

        except Exception as e:
            print(f"Error from E2B executor: {e}")
            rewards = [0.0] * len(completions)

        return rewards
    
    def __init__(self, thought_start_tag, thought_end_tag, answer_start_tag, answer_end_tag, language: str = "python"):
        self.thought_start_tag = thought_start_tag
        self.thought_end_tag = thought_end_tag
        self.answer_start_tag = answer_start_tag
        self.answer_end_tag = answer_end_tag
        self.language = language
        
    def extract_xml_thought(self, text: str) -> str:
        thought = text.split(self.thought_start_tag)[-1]
        thought = thought.split(self.thought_end_tag)[0]
        return thought.strip()
        
    def extract_xml_answer(self, text: str) -> str:
        answer = text.split(self.answer_start_tag)[-1]
        answer = answer.split(self.answer_end_tag)[0]
        return answer.strip()
    
    def correctness_reward_func(self, completions, answer, prompts, **kwargs) -> list[float]:
        responses = [completion[0]['content'] for completion in completions]
        extracted_responses = [self.extract_xml_answer(r) for r in responses]
        # print('-'*64)
        # print(f"Prompt:\n{prompts[0]}", f"\nAnswer:\n{answer[0]}", f"\nResponse:\n{responses[0]}")
        # print('-'*64)
        return [2.0 if r == a else 0.0 for r, a in zip(extracted_responses, answer)]

    def int_reward_func(self, completions, **kwargs) -> list[float]:
        responses = [completion[0]['content'] for completion in completions]
        extracted_responses = [self.extract_xml_answer(r) for r in responses]
        return [0.5 if r.isdigit() else 0.0 for r in extracted_responses]
    
    def strict_format_reward_func(self, completions, **kwargs):
        """Reward function that checks if the reasoning process is enclosed within <think> and </think> tags, while the final answer is enclosed within <answer> and </answer> tags."""
        pattern = rf"^{self.thought_start_tag}\n.*?\n{self.thought_end_tag}\n{self.answer_start_tag}\n.*?\n{self.answer_end_tag}$"
        completion_contents = [completion[0]["content"] for completion in completions]
        matches = [re.match(pattern, content, re.DOTALL | re.MULTILINE) for content in completion_contents]
        return [0.5 if match else 0.0 for match in matches]

    def soft_format_reward_func(self, completions, **kwargs) -> list[float]:
        """Reward function that checks if the completion has a specific format."""
        pattern = rf"^{self.thought_start_tag}\n.*?\n{self.thought_end_tag}\n{self.answer_start_tag}\n.*?\n{self.answer_end_tag}$"
        completion_contents = [completion[0]["content"] for completion in completions]
        matches = [re.match(pattern, content) for content in completion_contents]
        return [0.5 if match else 0.0 for match in matches]
    
    def code_format_reward(self, completions, **kwargs):
        """Format reward function specifically for code responses."""
        pattern = rf"^{self.thought_start_tag}\n.*?\n{self.thought_end_tag}\n{self.answer_start_tag}\n.*?```{self.language}.*?```.*?\n{self.answer_end_tag}$"
        completion_contents = [completion[0]["content"] for completion in completions]
        matches = [re.match(pattern, content, re.DOTALL | re.MULTILINE) for content in completion_contents]
        return [1.0 if match else 0.0 for match in matches]
    
    def get_mol_format_reward(self, completions, answer, prompts, **kwargs):
        """Format reward function specifically for molecule structure responses."""
        def multi_split(s: str, delimiters: list) -> list:
            """
            Splits the input string s using multiple delimiters provided in the delimiters list.
            
            Args:
                s (str): The string to split.
                delimiters (list): A list of delimiter strings to split on.
                
            Returns:
                list: A list of substrings after splitting s by the delimiters.
            """
            # Create a regular expression pattern that matches any of the delimiters.
            # re.escape is used to escape any characters that might be interpreted as regex metacharacters.
            regex_pattern = '|'.join(map(re.escape, delimiters))
            return re.split(regex_pattern, s)
        
        responses = [completion[0]['content'] for completion in completions]
        extracted_thoughts = [self.extract_xml_thought(r) for r in responses]
        extracted_responses = [self.extract_xml_answer(r) for r in responses]
        print('-'*64)
        print(f"Prompt:\n{prompts[0]}", f"\nAnswer:\n{answer[0]}", f"\nResponse:\n{responses[0]}")
        print('-'*64)
        
        src_embeddings = mol_model.encode(extracted_responses)
        dst_embeddings = mol_model.encode(answer)
        similarities = [mol_model.similarity(src_emb, dst_emb) for src_emb, dst_emb in zip(src_embeddings, dst_embeddings)]
        similarities = [1.0+(float(len(extracted_thoughts[idx]))*0.002) if f[0, 0]>mol_sim_threshold else 0.0 for idx, f in enumerate(similarities)]
        
        tokens_gens = [multi_split(f, ['[', ']']) for f in extracted_responses]
        tokens_gts = [multi_split(f, ['[', ']']) for f in answer]
        matcher = [1.0 if difflib.SequenceMatcher(None, tokens_gen, tokens_gt).ratio()>answer_len_ratio_threshold \
            and content.strip()[0]=='[' and content.strip()[-1]==']' else 0. \
                for tokens_gen, tokens_gt, content in zip(tokens_gens, tokens_gts, extracted_responses)]
        return [sim_score+match_score for sim_score, match_score in zip(similarities, matcher)]
    
    def tag_count_reward(self, completions, **kwargs) -> list[float]:
        """Reward function that checks if we produce the desired number of think and answer tags associated with `format_reward()`.

        Adapted from: https://gist.github.com/willccbb/4676755236bb08cab5f4e54a0475d6fb#file-grpo_demo-py-L90
        """
        def count_tags(text: str) -> float:
            count = 0.0
            # if text.count(f"{self.thought_start_tag}") == 1:
            #     count += 0.125
            if text.count(f"{self.thought_start_tag}\n") == 1:
                count += 0.125
            # if text.count(f"{self.thought_end_tag}") == 1:
            #     count += 0.125
            if text.count(f"\n{self.thought_end_tag}\n") == 1:
                count += 0.125
            # if text.count(f"{self.answer_start_tag}") == 1:
            #     count += 0.125
            if text.count(f"\n{self.answer_start_tag}\n") == 1:
                count += 0.125
            # if text.count(f"{self.answer_end_tag}") == 1:
            #     count += 0.125
            if text.count(f"\n{self.answer_end_tag}") == 1:
                count += 0.125
            return count

        contents = [completion[0]["content"] for completion in completions]
        return [count_tags(c) for c in contents]

    def xmlcount_reward_func(self, completions, **kwargs) -> list[float]:
        def count_xml(text: str, src_text: str=None) -> float:
            count = 0.0
            if text.count(f"{self.thought_start_tag}") == 1:
                count += 0.125
                text = text.split(self.thought_start_tag)[-1]
            if text.count(f"{self.thought_end_tag}") == 1:
                count += 0.125
                text = text.split(self.thought_end_tag)[-1]
            if text.count(f"{self.answer_start_tag}") == 1:
                count += 0.125
                if src_text is not None:
                    ntext = text.split(f"{self.answer_start_tag}")[-1]
                    ntext = ntext.split(f"{self.answer_end_tag}")[0]
                    if (min(len(ntext), len(src_text))/max(len(ntext), len(src_text)))<=answer_len_ratio_threshold:
                        count -= abs(len(ntext)-len(src_text))*0.001
                else:
                    count -= len(text.split(f"{self.answer_start_tag}")[-1])*0.001
                text = text.split(self.answer_start_tag)[-1]
            if text.count(f"{self.answer_end_tag}") == 1:
                count += 0.125
                count -= (len(text.split(f"{self.answer_end_tag}")[-1]) - 1)*0.001
            return count
    
        answer = []
        contents = [completion[0]["content"] for completion in completions]
        if 'answer' in kwargs.keys():
            answer = kwargs['answer']
        else:
            answer = [None for _ in contents]
        return [count_xml(c, ans) for c, ans in zip(contents, answer)]
    
    def grpo_transform(self, cfg, *args, **kwargs):
        def transform_fn(example, tokenizer=None):
            return example
        return transform_fn, {"remove_columns": ["thought"]}
    
    def get_reward_function_as_string(self, fn_name, verbose=True):
        fn_string = None
        static_vars = ['thought_start_tag', 'thought_end_tag', 'answer_start_tag', 'answer_end_tag', 'language']
        static_vars_string = '\n'.join(["{} = '{}'".format(f, getattr(self, f)) for f in static_vars])
        if verbose:
            print('Static variables:')
            print(static_vars_string)
        try:
            source_code = inspect.getsource(getattr(self, fn_name))
            source_code = source_code.replace('self, ', '')
            source_code = source_code.replace('self.', '')
            lines = source_code.splitlines()
            trimmed_lines = [line[4:] if line[:4] == '    ' else line for line in lines]
            source_code = '\n'.join(trimmed_lines)
            if verbose:
                print('Reward function:')
                print(source_code)
            fn_string = insert_static_var_string(source_code, static_vars_string)
            if verbose:
                print('Modified Reward function:')
                print(fn_string)
            pass
        except Exception as e:
            print(f"Reward function could not be accessed with error: {e}")
        return fn_string