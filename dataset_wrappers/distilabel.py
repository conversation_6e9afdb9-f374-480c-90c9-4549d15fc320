import os, sys
import collections
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict
from dataset_wrappers.wrapper import dpo_dataset

class distilabel_dataset(dpo_dataset):
    @staticmethod
    def standardize_feedback(
        dataset,
        aliases_for_system    = ["system",],
        aliases_for_user      = ["user", "human", "input",],
        aliases_for_assistant = ["gpt", "assistant", "output",],
    ):
        # Mapping for aliases
        aliases_mapping = {}
        for x in aliases_for_system:    aliases_mapping[x] = "system"
        for x in aliases_for_user:      aliases_mapping[x] = "user"
        for x in aliases_for_assistant: aliases_mapping[x] = "assistant"

        def _standardize_dataset(examples):
            system_prompts = examples["system"]
            input_prompts = examples["input"]
            chosen = examples["chosen"]
            rejected = examples["rejected"]
            all_prompts = []
            chosen_convos = []
            rejected_convos = []
            
            for idx in range(len(input_prompts)):
                new_prompt = [
                    { "role" : 'system', "content" : system_prompts[idx], },
                    { "role" : 'user', "content" : input_prompts[idx], },
                ]
                chosen_prompt = [
                    { "role" : 'assistant', "content" : chosen[idx], },
                ]
                rejected_prompt = [
                    { "role" : 'assistant', "content" : rejected[idx], },
                ]
                all_prompts.append(new_prompt)
                chosen_convos.append(chosen_prompt)
                rejected_convos.append(rejected_prompt)
            return {"prompt": all_prompts, "chosen" : chosen_convos, "rejected": rejected_convos}
        pass

        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', *args, **kwargs):
        super(distilabel_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, *args, **kwargs)
        if test_size>0.:
            self.iterable_dataset_traineval = self.iterable_dataset['train'].train_test_split(test_size=test_size)
            self.iterable_dataset_traineval['train'] = self.standardize_feedback(self.iterable_dataset_traineval['train'])
            self.iterable_dataset_traineval['test'] = self.standardize_feedback(self.iterable_dataset_traineval['test'])
        else:
            self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_feedback(self.iterable_dataset['train'])})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
        
    def __len__(self):
        return self.iterable_dataset_traineval[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset_traineval[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template, chat_headers={"system_header":None, "user_header":None, "assistant_header":None}):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            self.chat_headers = chat_headers
            self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True, fn_kwargs=chat_headers)
            for split_name in self.iterable_dataset_traineval.column_names:
                self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["prompt", "chosen", "rejected"]])
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None