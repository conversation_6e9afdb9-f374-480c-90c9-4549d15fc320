import os, sys
import collections
import math
from tqdm.notebook import tqdm
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict, Dataset
from dataset_wrappers.wrapper import sft_dataset
from datasets.utils.logging import disable_progress_bar, enable_progress_bar

class openassistant_dataset(sft_dataset):
    @staticmethod
    def standardize_sharegpt(
        dataset,
        aliases_for_system    = ["system",],
        aliases_for_user      = ["user", "prompter", "input", ],
        aliases_for_assistant = ["gpt", "assistant", "output",],
    ):
        disable_progress_bar()
        # Mapping for aliases
        aliases_mapping = {}
        for x in aliases_for_system:    aliases_mapping[x] = "system"
        for x in aliases_for_user:      aliases_mapping[x] = "user"
        for x in aliases_for_assistant: aliases_mapping[x] = "assistant"

        prompt_dataset = dataset.filter(lambda example: example['role']=="assistant")
        idx_cache = {}
        for idx, data_row in enumerate(tqdm(dataset)):
            idx_cache[data_row['message_id']] = idx
        pbar = tqdm(total=int(math.ceil(prompt_dataset.num_rows/1000)))
        
        def _get_convo_ids(parent_id, id_list):
            if parent_id is None:
                return id_list
            # data_rows = dataset.filter(lambda example: example['message_id'] == parent_id)
            data_rows = dataset.select([idx_cache[parent_id]])
            id_list = _get_convo_ids(data_rows['parent_id'][0] if data_rows.num_rows>0 else None, id_list)
            id_list.append(parent_id)
            return id_list
        
        def _set_message_idx(examples, id_list):
            message_ids = examples['message_id']
            message_idx = []
            for message_id in message_ids:
                message_idx.append(id_list.index(message_id))
            return({'message_idx': message_idx})
        
        def _standardize_dataset(examples):
            parent_ids = examples["parent_id"]
            message_ids = examples["message_id"]
            convos = []
            for idx in range(len(parent_ids)):
                convo_ids_list = _get_convo_ids(parent_ids[idx], [])
                # data_rows = dataset.filter(lambda example: example['message_id'] in convo_ids_list)
                convo_ids_list.append(message_ids[idx])
                data_rows = dataset.select([idx_cache[f] for f in convo_ids_list])
                data_rows = data_rows.map(_set_message_idx, batched = True, fn_kwargs = {'id_list': convo_ids_list})
                sorted_data_rows = data_rows.sort('message_idx')
                convo = []
                for row in sorted_data_rows:
                    convo.append({'role': aliases_mapping[row['role']], 'content': row['text']})
                convos.append(convo)
            pbar.update(1)
            return { "conversations": convos, }
        
        prompt_dataset = prompt_dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
        pbar.close()
        enable_progress_bar()
        return prompt_dataset
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', *args, **kwargs):
        super(openassistant_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, *args, **kwargs)
        if test_size>0.:
            self.iterable_dataset_traineval = DatasetDict({
                'train': self.standardize_sharegpt(self.iterable_dataset['train']),
                'test': self.standardize_sharegpt(self.iterable_dataset['validation']),
            })
        else:
            if split_name=='test':
                self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset['validation'])})
            else:
                self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset['train'])})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
    
    def __len__(self):
        return self.iterable_dataset_traineval[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset_traineval[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True)
            for split_name in self.iterable_dataset_traineval.column_names:
                self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["conversations", "text"]])
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None