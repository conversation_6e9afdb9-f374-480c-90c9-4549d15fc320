import os, sys
import re, collections
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta
from typing import Literal

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict
from dataset_wrappers.wrapper import grpo_dataset
from dataset_wrappers.rewards import reward_class

XML_COT_FORMAT = "<think>\n...\n</think>\n<answer>\n...\n</answer>"
GRPO_SYSTEM_PROMPT = "You are a helpful AI Assistant that provides well-reasoned and detailed responses. \n\n\
You first think about the reasoning process as an internal monologue and then provide the user with the answer. Respond in the following format: \n{}"

class gsm8k_dataset(grpo_dataset):
    @staticmethod
    def standardize_sharegpt(
        dataset,
        thought_start_tag = "<think>",
        thought_end_tag = "</think>",
        answer_start_tag = "<answer>",
        answer_end_tag = "</answer>",
        system_prompt = GRPO_SYSTEM_PROMPT,
    ):
        def _standardize_dataset(examples):
            questions = examples["question"]
            answers = examples["answer"]
            all_convos = []
            for convo in zip(questions, answers):
                thought_response = [f.strip() for f in convo[1].split('####')]
                new_convo = [
                    {'role': 'system', 'content': system_prompt,},
                    {'role': 'user', 'content': convo[0],},
                    {'role': 'assistant', 'content': "{}\n{}\n{}\n{}\n{}\n{}".format(
                            thought_start_tag,
                            thought_response[0],
                            thought_end_tag,
                            answer_start_tag,
                            thought_response[1],
                            answer_end_tag,
                        ),},
                ]
                all_convos.append(new_convo)
            pass
            return { "conversations" : all_convos, }
        pass

        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', download_split='main', system_prompt=None, response_template=None, *args, **kwargs):
        super(gsm8k_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, download_split=download_split, *args, **kwargs)
        self.system_prompt = system_prompt if system_prompt else GRPO_SYSTEM_PROMPT
        self.response_template = response_template if response_template else XML_COT_FORMAT
        self.system_prompt = self.system_prompt.format(self.response_template)
        
        tags = re.findall(r'<[^>]+>', self.response_template)
        print("System prompt:", self.system_prompt)
        print("Response tags:", tags)
        thought_start_tag = tags[0]
        thought_end_tag = tags[1]
        answer_start_tag = tags[2]
        answer_end_tag = tags[3]
        self.reward_model = reward_class(thought_start_tag, thought_end_tag, answer_start_tag, answer_end_tag)
        
        print(self.iterable_dataset)
        if test_size>0.:
            self.iterable_dataset_traineval = DatasetDict()
            self.iterable_dataset_traineval['train'] = self.standardize_sharegpt(self.iterable_dataset['train'], system_prompt=self.system_prompt)
            self.iterable_dataset_traineval['test'] = self.standardize_sharegpt(self.iterable_dataset['test'], system_prompt=self.system_prompt)
        else:
            self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset[split_name], system_prompt=self.system_prompt)})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
        
    def __len__(self):
        return self.iterable_dataset[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template, chat_headers={"system_header":None, "user_header":None, "assistant_header":None}, ft_type: Literal['sft', 'dpo', 'rl']='sft'):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            
            if ft_type=='sft':
                raise ValueError(f"{ft_type} not implemented.")
            elif ft_type=='dpo':
                raise ValueError(f"{ft_type} not implemented.")
            elif ft_type=='rl':
                self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_response_func, batched = True)
                for split_name in self.iterable_dataset_traineval.column_names:
                    self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["prompt", "thought", "answer"]])
            
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None
    
    def format_rewards(self, rewards_fn):
        assert self.reward_model
        reward_fn_list = [getattr(self.reward_model, f) for f in rewards_fn]
        assert len(reward_fn_list)>0
        return reward_fn_list