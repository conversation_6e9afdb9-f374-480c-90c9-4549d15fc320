import os, sys
import collections
from os import listdir
from os.path import isfile, isdir, join
from abc import ABCMeta

from unsloth.chat_templates import get_chat_template
from datasets import load_dataset, load_from_disk, concatenate_datasets, DatasetDict
from dataset_wrappers.wrapper import sft_dataset

SYSTEM_PROMPT = """\
You are a specialized AI for sentiment analysis focused on finance-related content. Your task is to classify tweets from the Twitter Financial News dataset into one of three sentiment categories:

1. Bearish: Indicates a negative sentiment toward the financial market or a particular financial event.
2. Bullish: Indicates a positive sentiment, suggesting optimism about financial prospects.
3. Neutral: Indicates neither a distinctly positive nor negative sentiment.

When processing a tweet, carefully analyze its language and context to determine which sentiment label best applies. Your classifications should directly reflect the sentiment expressed in the tweet regarding financial matters.
"""

USER_PROMPT = """\
TWEET:\n------\n\
{tweet}

Please respond with exactly one sentiment label: 'Bearish', 'Bullish', or 'Neutral'. Do not include any additional text.
"""

class twitter_financial_news_sentiment_dataset(sft_dataset):
    @staticmethod
    def standardize_sharegpt(dataset):
        sentiments = {
            0: "Bearish", 
            1: "Bullish", 
            2: "Neutral",
            "0": "Bearish", 
            "1": "Bullish", 
            "2": "Neutral",
            "LABEL_0": "Bearish", 
            "LABEL_1": "Bullish", 
            "LABEL_2": "Neutral",
        }  

        def _standardize_dataset(examples):
            convos_text = examples["text"]
            convos_label = examples["label"]
            all_convos = []
            for text, label in zip(convos_text, convos_label):
                new_convo = [
                    { "role" : "system", "content": SYSTEM_PROMPT},
                    { "role" : "user", "content": USER_PROMPT.format(tweet=text)},
                    { "role" : "assistant", "content": sentiments[label]},
                ]
                all_convos.append(new_convo)
            pass
            return { "conversations" : all_convos, }
        pass

        return dataset.map(_standardize_dataset, batched = True, desc = "Standardizing format")
    
    def __init__(self, dataset_name, cache_dir, test_size=0.1, split_name='train', *args, **kwargs):
        super(twitter_financial_news_sentiment_dataset, self).__init__(dataset_name=dataset_name, cache_dir=cache_dir, *args, **kwargs)
        if test_size>0.:
            self.iterable_dataset_traineval = DatasetDict({
                'train': self.standardize_sharegpt(self.iterable_dataset['train']),
                'test': self.standardize_sharegpt(self.iterable_dataset['validation']),
            })
        else:
            if split_name=='test':
                self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset['validation'])})
            else:
                self.iterable_dataset_traineval = DatasetDict({split_name: self.standardize_sharegpt(self.iterable_dataset[split_name])})
        self.split_name = split_name
        print(self.iterable_dataset_traineval)
    
    def __len__(self):
        return self.iterable_dataset_traineval[self.split_name].num_rows
    
    def __getitem__(self, idx):
        return self.iterable_dataset_traineval[self.split_name][idx]
    
    def format_dataset(self, tokenizer, chat_template):
        try:
            self.tokenizer = get_chat_template(tokenizer, chat_template = chat_template)
            self.iterable_dataset_traineval = self.iterable_dataset_traineval.map(self.formatting_prompts_func, batched = True)
            for split_name in self.iterable_dataset_traineval.column_names:
                self.iterable_dataset_traineval[split_name] = self.iterable_dataset_traineval[split_name].remove_columns([f for f in self.iterable_dataset_traineval[split_name].column_names if f not in ["conversations", "text"]])
            return self.tokenizer
        except Exception as e:
            print(f'Dataset chat formatting failed with error: {e}')
        return None