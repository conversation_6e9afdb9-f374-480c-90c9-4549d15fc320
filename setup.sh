#!/bin/bash

# IDSL LLM Framework Setup Script
# This script sets up the conda environment and installs all dependencies

set -e  # Exit on any error

echo "🚀 Setting up IDSL LLM Framework Environment"
echo "=============================================="

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "❌ Conda is not installed. Please install Anaconda or Miniconda first."
    echo "   Download from: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "✅ Conda found: $(conda --version)"

# Create conda environment
echo "📦 Creating conda environment 'idsl_llm'..."
if conda env list | grep -q "idsl_llm"; then
    echo "⚠️  Environment 'idsl_llm' already exists. Removing it..."
    conda env remove -n idsl_llm -y
fi

conda env create -f environment.yml

echo "✅ Environment created successfully!"

# Activate environment and install additional packages
echo "📦 Installing additional packages..."
eval "$(conda shell.bash hook)"
conda activate idsl_llm

# Install packages that might need special handling
echo "📦 Installing core ML packages..."
pip install torch transformers datasets accelerate peft trl

echo "📦 Installing quantization libraries..."
pip install bitsandbytes safetensors

echo "📦 Installing additional dependencies..."
pip install -r requirements.txt

# Make CLI script executable
echo "🔧 Setting up CLI script..."
chmod +x cli.sh

# Test the installation
echo "🧪 Testing installation..."
python test_framework.py --quick

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "To activate the environment, run:"
echo "   conda activate idsl_llm"
echo ""
echo "To test the framework, run:"
echo "   python test_framework.py"
echo ""
echo "To start training, use:"
echo "   ./cli.sh [backend] [training_type] [options]"
echo "   Example: ./cli.sh dynamic sft --training_config unsloth_sft_config.yml"
echo ""
echo "To start Jupyter notebook:"
echo "   jupyter notebook"
echo ""
echo "For help with CLI options:"
echo "   ./cli.sh help"
