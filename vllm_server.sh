#!/bin/bash
export VLLM_USE_MODELSCOPE=True

count_elements() {
    local input=$1
    if [ -z "$input" ]; then
        echo 0
        return
    fi
    IFS=',' read -ra parts <<< "$input"
    echo "${#parts[@]}"
}

MODEL_PATH="$1"
if test -f "$MODEL_PATH"; then
    echo "Model file $MODEL_PATH exists. Use file/directory path for gguf/vllm."
elif test -d "$MODEL_PATH"; then
    echo "Model directory $MODEL_PATH exists. Use file/directory path for gguf/vllm."
else
    echo "Model does not exist at $MODEL_PATH. Use file/directory path for gguf/vllm."
    return
fi

fixed_args=2
pattern='^([0-9]+)k$'

if [ $# -lt $fixed_args ]; then
    GPU_LAYERS=24
else
    GPU_LAYERS="${!fixed_args}"
    fixed_args=$(($fixed_args + 1))
fi
echo "Using gpu-offload-layers=$GPU_LAYERS"

if [[ $# -lt $fixed_args ]]; then
    MAX_TOKENS=8192
elif [[ ${!fixed_args} =~ $pattern ]]; then
    MAX_TOKENS="${BASH_REMATCH[1]}"
    let MAX_TOKENS=MAX_TOKENS
    MAX_TOKENS=$((MAX_TOKENS * 1024))
    fixed_args=$(($fixed_args + 1))
else
    MAX_TOKENS=8192
    fixed_args=$(($fixed_args + 1))
fi
echo "Using context-tokens=$MAX_TOKENS"
    
if [[ $# -lt $fixed_args ]]; then
    OPTIM_FLAG="vllm"
elif [[ ${!fixed_args} == "python" ]]; then
    OPTIM_FLAG="python"
    fixed_args=$(($fixed_args + 1))
elif [[ ${!fixed_args} == "bnb" ]]; then
    OPTIM_FLAG="bnb"
    fixed_args=$(($fixed_args + 1))
elif [[ ${!fixed_args} == "trl" ]]; then
    OPTIM_FLAG="trl"
    fixed_args=$(($fixed_args + 1))
elif [[ ${!fixed_args} == "axolotl" ]]; then
    OPTIM_FLAG="axolotl"
    fixed_args=$(($fixed_args + 1))
else
    OPTIM_FLAG="vllm"
    fixed_args=$(($fixed_args + 1))
fi

if [ -z "$SERVER_PORT" ]; then
    export SERVER_PORT=11434
    echo "SERVER_PORT is not set or is empty. Setting SERVER_PORT=$SERVER_PORT"
else
    echo "SERVER_PORT is set to '$SERVER_PORT'."
fi
AVAILABLE_PORT=$SERVER_PORT
check_condition() {
    return "$(./port_check.sh $AVAILABLE_PORT)"
}
while true; do
    if check_condition; then
        echo "Port $AVAILABLE_PORT is already bound."
        AVAILABLE_PORT=$((AVAILABLE_PORT + 1))
        echo "Checking Port $AVAILABLE_PORT"
    else
        echo "Port $AVAILABLE_PORT is available."
        break
    fi
    sleep 1
done
echo "Using Port $AVAILABLE_PORT"
sleep 1

if [ $OPTIM_FLAG == python ]; then
    echo "USING PYTHON SERVER"
    python3 -m llama_cpp.server \
    --model "$MODEL_PATH" \
    --host=0.0.0.0 \
    --port=$AVAILABLE_PORT \
    --n_ctx $MAX_TOKENS \
    --interrupt_requests False \
    --flash_attn True \
    --n_gpu_layers $GPU_LAYERS 
elif [ $OPTIM_FLAG == bnb ]; then
    echo "USING VLLM BNB SERVER"

    threshold() {
    local value=$1
    if (( $(echo "$value < 0.1" | bc -l) )); then
        echo 0.1
    elif (( $(echo "$value > 0.9" | bc -l) )); then
        echo 0.9
    else
        echo $value
    fi
    }
    
    GPU_MEM=$(threshold $GPU_LAYERS)
    gpu_count=$(count_elements $CUDA_VISIBLE_DEVICES)
    if [ "$gpu_count" -eq 0 ]; then
        gpu_count=$(nvidia-smi --list-gpus | wc -l)
    fi
    python ./get_jinja_template.py --MODEL $MODEL_PATH --CACHE "" --TOKEN $HF_TOKEN 

    # python -O -u -m vllm.entrypoints.openai.api_server \
    # --model=$MODEL_PATH \
    vllm serve $MODEL_PATH \
    --host=0.0.0.0 \
    --port=$AVAILABLE_PORT \
    --max-model-len $MAX_TOKENS \
    --gpu-memory-utilization=$GPU_MEM \
    --chat-template $MODEL_PATH/jinja_template.txt \
    --trust-remote-code \
    --enforce-eager \
    --quantization bitsandbytes --load-format bitsandbytes \
    --pipeline-parallel-size  $gpu_count
elif [ $OPTIM_FLAG == trl ]; then
    echo "USING VLLM TRL SERVER"

    threshold() {
    local value=$1
    if (( $(echo "$value < 0.1" | bc -l) )); then
        echo 0.1
    elif (( $(echo "$value > 0.9" | bc -l) )); then
        echo 0.9
    else
        echo $value
    fi
    }
    
    GPU_MEM=$(threshold $GPU_LAYERS)
    gpu_count=$(count_elements $CUDA_VISIBLE_DEVICES)
    if [ "$gpu_count" -eq 0 ]; then
        gpu_count=$(nvidia-smi --list-gpus | wc -l)
    fi

    trl vllm-serve \
    --model $MODEL_PATH \
    --host 0.0.0.0 \
    --port $AVAILABLE_PORT \
    --dtype auto \
    --max_model_len $MAX_TOKENS \
    --tensor_parallel_size $gpu_count \
    --gpu-memory-utilization $GPU_MEM
elif [ $OPTIM_FLAG == axolotl ]; then
    echo "USING VLLM AXOLOTL SERVER"

    threshold() {
    local value=$1
    if (( $(echo "$value < 0.1" | bc -l) )); then
        echo 0.1
    elif (( $(echo "$value > 0.9" | bc -l) )); then
        echo 0.9
    else
        echo $value
    fi
    }
    
    GPU_MEM=$(threshold $GPU_LAYERS)
    gpu_count=$(count_elements $CUDA_VISIBLE_DEVICES)
    if [ "$gpu_count" -eq 0 ]; then
        gpu_count=$(nvidia-smi --list-gpus | wc -l)
    fi

    axolotl vllm-serve \
    --model $MODEL_PATH \
    --host 0.0.0.0 \
    --port $AVAILABLE_PORT \
    --dtype auto \
    --max_model_len $MAX_TOKENS \
    --tensor_parallel_size $gpu_count \
    --gpu-memory-utilization $GPU_MEM
else
    echo "USING VLLM SERVER"

    threshold() {
    local value=$1
    if (( $(echo "$value < 0.1" | bc -l) )); then
        echo 0.1
    elif (( $(echo "$value > 0.9" | bc -l) )); then
        echo 0.9
    else
        echo $value
    fi
    }
    GPU_MEM=$(threshold $GPU_LAYERS)

    python -O -u -m vllm.entrypoints.openai.api_server \
    --host=0.0.0.0 \
    --port=$AVAILABLE_PORT \
    --trust-remote-code \
    --max-model-len $MAX_TOKENS \
    --gpu-memory-utilization=$GPU_MEM \
    --model=$MODEL_PATH \
    --tensor-parallel-size $(nvidia-smi --list-gpus | wc -l)
fi
