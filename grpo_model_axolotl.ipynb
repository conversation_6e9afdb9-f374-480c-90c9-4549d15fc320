{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, sys\n", "from os import listdir\n", "from os.path import isfile, isdir, join\n", "from tqdm.notebook import tqdm\n", "from dotenv import load_dotenv\n", "os.environ['LD_LIBRARY_PATH'] = '/usr/lib/x86_64-linux-gnu'\n", "os.environ[\"CUDA_DEVICE_ORDER\"] = \"PCI_BUS_ID\"\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle as pkl\n", "import math, random\n", "import cv2, imutils\n", "import json, string, yaml\n", "import shutil, gc, copy\n", "import multiprocessing\n", "import subprocess, asyncio\n", "import argparse\n", "import importlib\n", "from pprint import PrettyPrinter as pp\n", "from argparse import Namespace\n", "from datetime import datetime\n", "from PIL import Image\n", "from typing import Iterable, Callable, Dict, Literal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.tensorboard import SummaryWriter\n", "from torch.utils.data import DataLoader, Dataset\n", "from torch.nn.utils.rnn import pad_sequence\n", "from torchinfo import summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import get_linear_schedule_with_warmup, pipeline, TextStreamer\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, AutoConfig\n", "from transformers import GPTQConfig, TrainerCallback, DataCollatorForLanguageModeling, DataCollatorForSeq2Seq\n", "from transformers import BitsAndBytesConfig, TrainingArguments, EvalPrediction, logging\n", "from tokenizers import tokenizers\n", "from evaluate import load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import huggingface_hub\n", "import accelerate\n", "import bitsandbytes as bnb\n", "from trl import SFTTrainer, DPOTrainer, GRPOTrainer, DPOConfig, GRPOConfig\n", "from peft import LoraConfig, LoftQConfig, PeftModel, TaskType, get_peft_model, prepare_model_for_kbit_training\n", "from trainer.rope_scaling import ROPE_CONFIG"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def in_notebook() -> bool:\n", "    try:\n", "        # Try to get the name of the current shell.\n", "        shell = get_ipython().__class__.__name__\n", "        if shell == 'ZMQInteractiveShell':\n", "            # <PERSON><PERSON><PERSON> notebook or qtconsole\n", "            return True\n", "        elif shell == 'TerminalInteractiveShell':\n", "            # Terminal running IPython\n", "            return False\n", "        else:\n", "            # Other type (unknown)\n", "            return False\n", "    except NameError:\n", "        # get_ipython is not defined, likely not running in IPython at all.\n", "        return False\n", "if in_notebook() or \"--training_config\" not in sys.argv or 'DEFAULT' in sys.argv:\n", "    sys.argv = [\n", "        'script.py',\n", "        '--training_config', './axolotl_grpo_config.yml',\n", "        '--cache_dir', '/data/server_cache',\n", "        '--max_seq_len', '16384',\n", "        '--adapter_type', 'QLoRA',\n", "        '--quant_bits', '4',\n", "        '--gpu_parallel', '1',\n", "        '--lora_rank', '16',\n", "        '--lora_dropout', '0',\n", "        '--save_gguf', 'q4_k_m',\n", "        '--save_merged',\n", "        '--rl',\n", "    ]\n", "\n", "pprinter = pp(indent=2)\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument('--training_config', type=str, required=True)\n", "parser.add_argument('--accelerator_config', type=str, default='./accelerator_config.yml')\n", "parser.add_argument('--model_name', type=str, default='meta-llama/Llama-3.2-3B-Instruct')\n", "parser.add_argument('--cache_dir', type=str, default='../server_cache')\n", "parser.add_argument('--reference_model_path', type=str, required=False)\n", "parser.add_argument('--lora_rank', type=int, required=False, default=16)\n", "parser.add_argument('--lora_alpha', type=int, required=False, default=0)\n", "parser.add_argument('--lora_dropout', type=float, required=False, default=0.1)\n", "parser.add_argument('--gpu_parallel', type=int, required=False, default=1)\n", "parser.add_argument('--max_seq_len', type=int, required=False, default=32768)\n", "parser.add_argument('--quant_bits', type=int, required=False, default=4, choices=[4, 8])\n", "parser.add_argument('--adapter_type', type=str, required=False, default='LoRA', choices=['LoRA', 'QLoRA', 'LoftQ'])\n", "parser.add_argument('--save_gguf', type=str, required=False, default='')\n", "parser.add_argument('--sft', action='store_true', required=False)\n", "parser.add_argument('--dpo', action='store_true', required=False)\n", "parser.add_argument('--rl', action='store_true', required=False)\n", "parser.add_argument('--save_merged', action='store_true', required=False)\n", "args = parser.parse_args()\n", "with open(args.training_config, 'r') as file:\n", "    training_config_dict = yaml.safe_load(file)\n", "with open(args.accelerator_config, 'r') as file:\n", "    accelerator_config_dict = yaml.safe_load(file)\n", "\n", "if not isdir(args.cache_dir):\n", "    os.mkdir(args.cache_dir)\n", "assert isdir(args.cache_dir)\n", "if 'model_name' in training_config_dict['hub_config'].keys():\n", "    args.model_name = training_config_dict['hub_config']['model_name'] if training_config_dict['hub_config']['model_name'] is not None else args.model_name\n", "print(args)\n", "pprinter.pprint(training_config_dict)\n", "assert args.sft or args.dpo or args.rl\n", "VLLM_CUDA_VISIBLE_DEVICES = [f for f in range(accelerator_config_dict['num_processes'])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def stream_command(command):\n", "    # Open the process with stdout piped and merge stderr into stdout.\n", "    process = subprocess.Popen(\n", "        command,\n", "        shell=True,\n", "        stdout=subprocess.PIPE,\n", "        stderr=subprocess.STDOUT,\n", "        text=True  # Use text mode to get strings instead of bytes.\n", "    )\n", "    \n", "    # Continuously read output line by line.\n", "    while True:\n", "        output = process.stdout.readline()\n", "        if output == '' and process.poll() is not None:\n", "            break\n", "        if output:\n", "            print(output.strip())\n", "    \n", "    # Optionally, return the command's exit code.\n", "    return process.poll()\n", "\n", "async def astream_command(command: str, polling_timeout: float = 5.0, chunk_size: int = 32):\n", "    # Start the subprocess asynchronously.\n", "    process = await asyncio.create_subprocess_shell(\n", "        command,\n", "        stdout=asyncio.subprocess.PIPE,\n", "        stderr=asyncio.subprocess.STDOUT\n", "    )\n", "    \n", "    # Continuously read output line by line.\n", "    while True:\n", "        try:\n", "            line = await asyncio.wait_for(process.stdout.read(chunk_size), timeout=polling_timeout)\n", "            if not line:\n", "                break\n", "            line = line.decode(\"utf-8\", errors='replace')\n", "        except asyncio.TimeoutError:\n", "            yield \"\"\n", "        except Exception as e:\n", "            line = None\n", "        finally:\n", "            if line is not None and type(line)==str:\n", "                yield line\n", "    \n", "    # Wait for the process to complete.\n", "    await process.wait()\n", "    yield \"<END_OF_STREAM>\"\n", "    \n", "async def run_astream_command(command: str):\n", "    print(command)\n", "    try:\n", "        async for ln in astream_command(command):\n", "            if ln==\"<END_OF_STREAM>\":\n", "                break\n", "            sys.stdout.write(ln)\n", "            sys.stdout.flush()\n", "    except Exception as e:\n", "        sys.stdout.write(f\"\\nStream ended with exception: {e}\\n\")\n", "        sys.stdout.flush()\n", "        return False\n", "    return True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["axolotl_config = \"\"\"\n", "base_model: {base_model}\n", "tokenizer_config: {tokenizer_config}\n", "model_type: {model_type}\n", "tokenizer_type: {tokenizer_type}\n", "trust_remote_code: {trust_remote_code}\n", "\n", "{rl_type}\n", "\n", "{trl_args}\n", "\n", "{vllm_args}\n", "  \n", "bnb_config_kwargs:\n", "  bnb_4bit_quant_type: nf4\n", "  bnb_4bit_use_double_quant: {bnb_4bit_use_double_quant}\n", "  llm_int8_has_fp16_weight: {llm_int8_has_fp16_weight}\n", "  \n", "load_in_8bit: {load_in_8bit}\n", "load_in_4bit: {load_in_4bit}\n", "bf16: auto\n", "fp16: {fp16}\n", "bfloat16: {bfloat16}\n", "float16: {float16}\n", "\n", "chat_template: jinja\n", "chat_template_jinja: {chat_template_jinja}\n", "sequence_len: {sequence_len}\n", "sample_packing: false\n", "pad_to_sequence_len: false\n", "\n", "datasets:\n", "  - path: {dataset_path}\n", "    train_on_split: train\n", "    type: chat_template\n", "    chat_template: jinja\n", "    chat_template_jinja: {dataset_chat_template_jinja}\n", "    field_messages: {field_messages}\n", "    roles_to_train: {roles_to_train}\n", "    train_on_eos: {train_on_eos} # all | turn | last\n", "    roles:\n", "      user: [\"user\"]\n", "      assistant: [\"assistant\"]\n", "      system: [\"system\"]\n", "      tool: [\"tool\"]\n", "    {field_chosen_rejected}\n", "\n", "test_datasets:\n", "  - path: {test_dataset_path}\n", "    split: test\n", "    type: chat_template\n", "    chat_template: jinja\n", "    chat_template_jinja: {test_dataset_chat_template_jinja}\n", "    field_messages: {test_field_messages}\n", "    roles:\n", "      user: [\"user\"]\n", "      assistant: [\"assistant\"]\n", "      system: [\"system\"]\n", "      tool: [\"tool\"]\n", "\n", "output_dir: {output_dir}\n", "dataset_prepared_path: {dataset_prepared_path}\n", "hub_model_id: {hub_model_id}\n", "hub_strategy: {hub_strategy}\n", "\n", "adapter: {adapter}\n", "lora_model_dir: {lora_model_dir}\n", "lora_r: {lora_r}\n", "lora_alpha: {lora_alpha}\n", "lora_dropout: {lora_dropout}\n", "lora_target_linear: {lora_target_linear}\n", "lora_target_modules:\n", "  {lora_target_modules}\n", "\n", "lora_fan_in_fan_out:\n", "{loftq_bits}\n", "\n", "use_tensorboard: true\n", "gradient_accumulation_steps: {gradient_accumulation_steps}\n", "micro_batch_size: {micro_batch_size}\n", "eval_batch_size: {eval_batch_size}\n", "num_epochs: {num_epochs}\n", "warmup_ratio: {warmup_ratio}\n", "learning_rate: {learning_rate}\n", "weight_decay: {weight_decay}\n", "max_grad_norm: {max_grad_norm}\n", "eval_steps: {eval_steps}\n", "save_steps: {save_steps}\n", "eval_strategy: {eval_strategy}\n", "save_total_limit: {save_total_limit}\n", "max_steps: {max_steps}\n", "logging_steps: {logging_steps}\n", "warmup_steps: 0\n", "\n", "eval_causal_lm_metrics: [\"sacrebleu\", \"comet\", \"ter\", \"chrf\", \"perplexity\"]\n", "auto_find_batch_size: {auto_find_batch_size}\n", "save_safetensors: {save_safetensors}\n", "train_on_inputs: false\n", "group_by_length: true\n", "gradient_checkpointing: true\n", "\n", "dataset_prepared_path: {dataset_prepared_path}\n", "early_stopping_patience: {early_stopping_patience}\n", "lr_scheduler: {lr_scheduler}\n", "optimizer: {optimizer}\n", "\n", "special_tokens:\n", "\n", "flash_attention: true\n", "deepspeed: {deepspeed}\n", "debug: false\n", "seed: 42\n", "strict: false\n", "use_tensorboard: {use_tensorboard}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vllm_args = \"\"\"vllm:\n", "  dtype: auto\n", "  gpu_memory_utilization: 0.85\n", "  host: {VLLM_HOST}\n", "  port: {VLLM_PORT}\n", "  tensor_parallel_size: {gpu_count}\n", "  max_model_len: {max_model_len}\n", "\"\"\"\n", "\n", "trl_args = \"\"\"trl:\n", "  use_vllm: {use_vllm}\n", "  log_completions: true\n", "  vllm_server_host: 0.0.0.0\n", "  vllm_server_port: 8000\n", "  vllm_server_timeout: 120\n", "    \n", "  # vllm_device: \n", "  # vllm_gpu_memory_utilization: \n", "  # vllm_max_model_len: \n", "  # vllm_dtype: \n", "  # sync_ref_model: \n", "  # ref_model_sync_steps: \n", "  # ref_model_mixup_alpha: \n", "  \n", "  beta: {grpo_beta}\n", "  max_completion_length: {grpo_max_completion_length}\n", "  reward_funcs: {grpo_reward_funcs}\n", "  reward_weights: {grpo_reward_weights}\n", "  num_generations: {grpo_num_generations}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_config_sft(\n", "    axolotl_config, \n", "    args,\n", "    model_path, \n", "    dataset_path, \n", "    training_config_dict,\n", "    bnb_config_dict,\n", "    dataset_obj,\n", "    lora_model_dir = None,\n", "    trust_remote_code = True\n", "):\n", "    axolotl_config_parsed = list(string.Formatter.parse(\"\", axolotl_config))\n", "    axolotl_config_keys = [f[1] for f in axolotl_config_parsed if f[1]]\n", "    axolotl_config_dict = {f: '' for f in axolotl_config_keys}\n", "    axolotl_config_dict['rl_type'] = \"\"\n", "    axolotl_config_dict['trl_args'] = \"\"\n", "\n", "    axolotl_config_dict['base_model'] = model_path\n", "    axolotl_config_dict['model_type'] = training_config_dict['training_arguments']['model_type']\n", "    axolotl_config_dict['tokenizer_type'] = training_config_dict['training_arguments']['tokenizer_type']\n", "    axolotl_config_dict['output_dir'] = training_config_dict['training_arguments']['output_dir']\n", "    axolotl_config_dict['trust_remote_code'] = 'true' if trust_remote_code else 'false'\n", "\n", "    axolotl_config_dict['sequence_len'] = args.max_seq_len\n", "    axolotl_config_dict['dataset_path'] = dataset_path\n", "    axolotl_config_dict['field_messages'] = \"conversations\"\n", "    axolotl_config_dict['test_field_messages'] = \"conversations\"\n", "    axolotl_config_dict['roles_to_train'] = training_config_dict['dataset_arguments']['roles_to_train']\n", "    axolotl_config_dict['train_on_eos'] = training_config_dict['dataset_arguments']['train_on_eos']\n", "    axolotl_config_dict['test_dataset_path'] = dataset_path\n", "    \n", "    axolotl_config_dict['micro_batch_size'] = max(1, \n", "        training_config_dict['training_arguments']['per_device_train_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "    axolotl_config_dict['eval_batch_size'] = max(1, \n", "        training_config_dict['training_arguments']['per_device_eval_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "    axolotl_config_dict['gradient_accumulation_steps'] = training_config_dict['training_arguments']['gradient_accumulation_steps']\n", "    axolotl_config_dict['num_epochs'] = training_config_dict['training_arguments']['num_train_epochs']\n", "    axolotl_config_dict['warmup_ratio'] = training_config_dict['training_arguments']['warmup_ratio']\n", "    axolotl_config_dict['learning_rate'] = training_config_dict['training_arguments']['learning_rate']\n", "    axolotl_config_dict['weight_decay'] = training_config_dict['training_arguments']['weight_decay']\n", "    axolotl_config_dict['max_grad_norm'] = training_config_dict['training_arguments']['max_grad_norm']\n", "    axolotl_config_dict['eval_steps'] = training_config_dict['training_arguments']['eval_steps']\n", "    axolotl_config_dict['save_steps'] = training_config_dict['training_arguments']['save_steps']\n", "    axolotl_config_dict['logging_steps'] = training_config_dict['training_arguments']['logging_steps']\n", "    axolotl_config_dict['save_total_limit'] = training_config_dict['training_arguments']['save_total_limit']\n", "    axolotl_config_dict['lr_scheduler'] = training_config_dict['training_arguments']['lr_scheduler_type']\n", "    axolotl_config_dict['optimizer'] = training_config_dict['training_arguments']['optim']\n", "    axolotl_config_dict['use_tensorboard'] = 'true' if training_config_dict['training_arguments']['report_to']=='tensorboard' else 'false'\n", "    axolotl_config_dict['eval_strategy'] = 'no' if training_config_dict['dataset_arguments']['test_split']==0 else ''\n", "    axolotl_config_dict['deepspeed'] = join(os.getcwd(), 'deepspeed_configs/zero2.json')\n", "\n", "    dataset_prepared_path = join(args.cache_dir, 'axolotl_prepared_dataset')\n", "    if isdir(dataset_prepared_path):\n", "        os.system(f'rm -rf \"{dataset_prepared_path}\"')\n", "    axolotl_config_dict['dataset_prepared_path'] = dataset_prepared_path\n", "\n", "    if args.adapter_type=='LoRA':\n", "        target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                        \"gate_proj\", \"up_proj\", \"down_proj\",]\n", "        axolotl_config_dict['adapter'] = 'lora'\n", "        axolotl_config_dict['lora_target_modules'] = '\\n  '.join([f'- {f}' for f in target_modules])\n", "    if args.adapter_type=='QLoRA':\n", "        axolotl_config_dict['adapter'] = 'qlora'\n", "        axolotl_config_dict['lora_target_linear'] = 'true'\n", "    if args.adapter_type=='LoftQ':\n", "        axolotl_config_dict['adapter'] = 'lora'\n", "        axolotl_config_dict['lora_target_linear'] = 'true'\n", "        axolotl_config_dict['loftq_bits'] = \"peft:\\n  loftq_config:\\n    loftq_bits: {}\".format(args.quant_bits)\n", "    axolotl_config_dict['lora_r'] = args.lora_rank\n", "    axolotl_config_dict['lora_alpha'] = args.lora_alpha if args.lora_alpha else args.lora_rank\n", "    axolotl_config_dict['lora_dropout'] = args.lora_dropout\n", "    axolotl_config_dict['lora_model_dir'] = lora_model_dir if lora_model_dir else ''\n", "\n", "    for k in bnb_config_dict.keys():\n", "        axolotl_config_dict[k] = 'true' if bnb_config_dict[k] else 'false'\n", "\n", "    axolotl_config_yml = yaml.safe_load(axolotl_config.format(**axolotl_config_dict))\n", "    axolotl_config_yml['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['test_datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['special_tokens'] = {'pad_token': dataset_obj.tokenizer.pad_token if dataset_obj.tokenizer.pad_token else dataset_obj.tokenizer.eos_token}\n", "    axolotl_config_yml = yaml.dump(axolotl_config_yml, default_flow_style=False, sort_keys=False)\n", "    axolotl_config_yml = axolotl_config_yml.replace('null', '')\n", "\n", "    axolotl_config_path = join(training_config_dict['training_arguments']['output_dir'], f'{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.yml')\n", "    if not isdir(training_config_dict['training_arguments']['output_dir']):\n", "        os.mkdir(training_config_dict['training_arguments']['output_dir'])\n", "    with open(axolotl_config_path, 'w') as fp:\n", "        fp.write(axolotl_config_yml)\n", "    return axolotl_config_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_config_dpo(\n", "    axolotl_config, \n", "    args,\n", "    model_path, \n", "    dataset_path, \n", "    training_config_dict,\n", "    bnb_config_dict,\n", "    dataset_obj,\n", "    lora_model_dir = None,\n", "    trust_remote_code = True\n", "):\n", "    axolotl_config_parsed = list(string.Formatter.parse(\"\", axolotl_config))\n", "    axolotl_config_keys = [f[1] for f in axolotl_config_parsed if f[1]]\n", "    axolotl_config_dict = {f: '' for f in axolotl_config_keys}\n", "    custom_dpo_chat_template = \"{% for message in messages %}{% if message['role'] == 'user' %}{{ message['content'] }}{% elif message['role'] == 'assistant' %}{{ message['content'] }}{% endif %}{% endfor %}\"\n", "    axolotl_config_dict['rl_type'] = \"rl: dpo\"\n", "    axolotl_config_dict['trl_args'] = \"dpo_use_weighting: {}\\nrpo_alpha: {}\".format(\n", "        training_config_dict['trl_arguments']['dpo_use_weighting'], \n", "        training_config_dict['trl_arguments']['rpo_alpha']\n", "    )\n", "\n", "    axolotl_config_dict['base_model'] = model_path\n", "    axolotl_config_dict['model_type'] = training_config_dict['training_arguments']['model_type']\n", "    axolotl_config_dict['tokenizer_type'] = training_config_dict['training_arguments']['tokenizer_type']\n", "    axolotl_config_dict['output_dir'] = training_config_dict['training_arguments']['output_dir']\n", "    axolotl_config_dict['trust_remote_code'] = 'true' if trust_remote_code else 'false'\n", "\n", "    axolotl_config_dict['sequence_len'] = args.max_seq_len\n", "    axolotl_config_dict['dataset_path'] = dataset_path\n", "    axolotl_config_dict['field_messages'] = \"prompt\"\n", "    axolotl_config_dict['test_field_messages'] = \"prompt\"\n", "    axolotl_config_dict['field_chosen_rejected'] = 'field_chosen: {field_chosen}\\n    field_rejected: {field_rejected}'.format(\n", "        field_chosen = \"chosen\",\n", "        field_rejected = \"rejected\",\n", "    )\n", "    axolotl_config_dict['roles_to_train'] = training_config_dict['dataset_arguments']['roles_to_train']\n", "    axolotl_config_dict['train_on_eos'] = training_config_dict['dataset_arguments']['train_on_eos']\n", "    axolotl_config_dict['test_dataset_path'] = dataset_path\n", "\n", "    axolotl_config_dict['micro_batch_size'] = max(1, \n", "        training_config_dict['training_arguments']['per_device_train_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "    axolotl_config_dict['eval_batch_size'] = max(1, \n", "        training_config_dict['training_arguments']['per_device_eval_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "    axolotl_config_dict['gradient_accumulation_steps'] = training_config_dict['training_arguments']['gradient_accumulation_steps']\n", "    axolotl_config_dict['num_epochs'] = training_config_dict['training_arguments']['num_train_epochs']\n", "    axolotl_config_dict['warmup_ratio'] = training_config_dict['training_arguments']['warmup_ratio']\n", "    axolotl_config_dict['learning_rate'] = training_config_dict['training_arguments']['learning_rate']\n", "    axolotl_config_dict['weight_decay'] = training_config_dict['training_arguments']['weight_decay']\n", "    axolotl_config_dict['max_grad_norm'] = training_config_dict['training_arguments']['max_grad_norm']\n", "    axolotl_config_dict['eval_steps'] = training_config_dict['training_arguments']['eval_steps']\n", "    axolotl_config_dict['save_steps'] = training_config_dict['training_arguments']['save_steps']\n", "    axolotl_config_dict['logging_steps'] = training_config_dict['training_arguments']['logging_steps']\n", "    axolotl_config_dict['save_total_limit'] = training_config_dict['training_arguments']['save_total_limit']\n", "    axolotl_config_dict['lr_scheduler'] = training_config_dict['training_arguments']['lr_scheduler_type']\n", "    axolotl_config_dict['optimizer'] = training_config_dict['training_arguments']['optim']\n", "    axolotl_config_dict['use_tensorboard'] = 'true' if training_config_dict['training_arguments']['report_to']=='tensorboard' else 'false'\n", "    axolotl_config_dict['eval_strategy'] = 'no' if training_config_dict['dataset_arguments']['test_split']==0 else ''\n", "    axolotl_config_dict['deepspeed'] = join(os.getcwd(), 'deepspeed_configs/zero2.json')\n", "\n", "    dataset_prepared_path = join(args.cache_dir, 'axolotl_prepared_dataset')\n", "    if isdir(dataset_prepared_path):\n", "        os.system(f'rm -rf \"{dataset_prepared_path}\"')\n", "    axolotl_config_dict['dataset_prepared_path'] = dataset_prepared_path\n", "\n", "    if args.adapter_type=='LoRA':\n", "        target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                        \"gate_proj\", \"up_proj\", \"down_proj\",]\n", "        axolotl_config_dict['adapter'] = 'lora'\n", "        axolotl_config_dict['lora_target_modules'] = '\\n  '.join([f'- {f}' for f in target_modules])\n", "    if args.adapter_type=='QLoRA':\n", "        axolotl_config_dict['adapter'] = 'qlora'\n", "        axolotl_config_dict['lora_target_linear'] = 'true'\n", "    if args.adapter_type=='LoftQ':\n", "        axolotl_config_dict['adapter'] = 'lora'\n", "        axolotl_config_dict['lora_target_linear'] = 'true'\n", "        axolotl_config_dict['loftq_bits'] = \"peft:\\n  loftq_config:\\n    loftq_bits: {}\".format(args.quant_bits)\n", "    axolotl_config_dict['lora_r'] = args.lora_rank\n", "    axolotl_config_dict['lora_alpha'] = args.lora_alpha if args.lora_alpha else args.lora_rank\n", "    axolotl_config_dict['lora_dropout'] = args.lora_dropout\n", "    axolotl_config_dict['lora_model_dir'] = lora_model_dir if lora_model_dir else ''\n", "\n", "    for k in bnb_config_dict.keys():\n", "        axolotl_config_dict[k] = 'true' if bnb_config_dict[k] else 'false'\n", "\n", "    axolotl_config_yml = yaml.safe_load(axolotl_config.format(**axolotl_config_dict))\n", "    axolotl_config_yml['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['datasets'][0]['chat_template_jinja'] = custom_dpo_chat_template\n", "    axolotl_config_yml['test_datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['special_tokens'] = {'pad_token': dataset_obj.tokenizer.pad_token if dataset_obj.tokenizer.pad_token else dataset_obj.tokenizer.eos_token}\n", "    axolotl_config_yml = yaml.dump(axolotl_config_yml, default_flow_style=False, sort_keys=False)\n", "    axolotl_config_yml = axolotl_config_yml.replace('null', '')\n", "\n", "    axolotl_config_path = join(training_config_dict['training_arguments']['output_dir'], f'{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.yml')\n", "    if not isdir(training_config_dict['training_arguments']['output_dir']):\n", "        os.mkdir(training_config_dict['training_arguments']['output_dir'])\n", "    with open(axolotl_config_path, 'w') as fp:\n", "        fp.write(axolotl_config_yml)\n", "    return axolotl_config_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def format_config_rl(\n", "    axolotl_config, \n", "    args,\n", "    trl_args, \n", "    model_path, \n", "    dataset_path, \n", "    training_config_dict,\n", "    bnb_config_dict,\n", "    dataset_obj,\n", "    lora_model_dir = None,\n", "    trust_remote_code = True\n", "):\n", "    axolotl_config_parsed = list(string.Formatter.parse(\"\", axolotl_config))\n", "    axolotl_config_keys = [f[1] for f in axolotl_config_parsed if f[1]]\n", "    axolotl_config_dict = {f: '' for f in axolotl_config_keys}\n", "    axolotl_config_dict['rl_type'] = \"rl: grpo\"\n", "    if len(VLLM_CUDA_VISIBLE_DEVICES)//2>0:\n", "        # axolotl_config_dict['vllm_args'] = vllm_args.format(gpu_count=len(VLLM_CUDA_VISIBLE_DEVICES)//2, \n", "        #                                                     max_model_len=args.max_seq_len,\n", "        #                                                     VLLM_HOST=os.getenv('VLLM_HOST'),\n", "        #                                                     VLLM_PORT=os.getenv('VLLM_PORT'))\n", "        axolotl_config_dict['trl_args'] = trl_args.format(\n", "            use_vllm = \"true\",\n", "            grpo_beta = training_config_dict['trl_arguments']['grpo_beta'] if training_config_dict['trl_arguments']['grpo_beta'] else '', \n", "            grpo_num_generations = training_config_dict['trl_arguments']['grpo_num_generations'] if training_config_dict['trl_arguments']['grpo_num_generations'] else '',\n", "            grpo_max_completion_length = args.max_seq_len-training_config_dict['trl_arguments']['grpo_max_completion_length'] if training_config_dict['trl_arguments']['grpo_max_completion_length'] else '',\n", "            grpo_reward_weights = str(training_config_dict['trl_arguments']['grpo_reward_weights']) if training_config_dict['trl_arguments']['grpo_reward_weights'] else '',\n", "            grpo_reward_funcs = str(training_config_dict['trl_arguments']['grpo_reward_funcs']) if training_config_dict['trl_arguments']['grpo_reward_funcs'] else '',\n", "        )\n", "    else:\n", "        axolotl_config_dict['trl_args'] = trl_args.format(\n", "            use_vllm = \"false\",\n", "            grpo_beta = training_config_dict['trl_arguments']['grpo_beta'] if training_config_dict['trl_arguments']['grpo_beta'] else '', \n", "            grpo_num_generations = training_config_dict['trl_arguments']['grpo_num_generations'] if training_config_dict['trl_arguments']['grpo_num_generations'] else '',\n", "            grpo_max_completion_length = args.max_seq_len-training_config_dict['trl_arguments']['grpo_max_completion_length'] if training_config_dict['trl_arguments']['grpo_max_completion_length'] else '',\n", "            grpo_reward_weights = str(training_config_dict['trl_arguments']['grpo_reward_weights']) if training_config_dict['trl_arguments']['grpo_reward_weights'] else '',\n", "            grpo_reward_funcs = str(training_config_dict['trl_arguments']['grpo_reward_funcs']) if training_config_dict['trl_arguments']['grpo_reward_funcs'] else '',\n", "        )\n", "\n", "    axolotl_config_dict['base_model'] = model_path\n", "    axolotl_config_dict['model_type'] = training_config_dict['training_arguments']['model_type']\n", "    axolotl_config_dict['tokenizer_type'] = training_config_dict['training_arguments']['tokenizer_type']\n", "    axolotl_config_dict['output_dir'] = training_config_dict['training_arguments']['output_dir']\n", "    axolotl_config_dict['trust_remote_code'] = 'true' if trust_remote_code else 'false'\n", "\n", "    axolotl_config_dict['sequence_len'] = args.max_seq_len\n", "    axolotl_config_dict['dataset_path'] = dataset_path\n", "    axolotl_config_dict['field_messages'] = \"prompt\"\n", "    axolotl_config_dict['test_field_messages'] = \"conversations\"\n", "    axolotl_config_dict['roles_to_train'] = training_config_dict['dataset_arguments']['roles_to_train']\n", "    axolotl_config_dict['train_on_eos'] = training_config_dict['dataset_arguments']['train_on_eos']\n", "    axolotl_config_dict['test_dataset_path'] = dataset_path\n", "    # axolotl_config_dict['field_chosen_rejected'] = 'field_chosen: {field_chosen}\\n    field_rejected: {field_rejected}'.format(\n", "    #     field_chosen = \"chosen\",\n", "    #     field_rejected = \"rejected\",\n", "    # )\n", "    \n", "    axolotl_config_dict['micro_batch_size'] = max(1, \n", "        training_config_dict['training_arguments']['per_device_train_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "    axolotl_config_dict['eval_batch_size'] = max(1, \n", "        training_config_dict['training_arguments']['per_device_eval_batch_size']//training_config_dict['training_arguments']['gradient_accumulation_steps'])\n", "    axolotl_config_dict['gradient_accumulation_steps'] = training_config_dict['training_arguments']['gradient_accumulation_steps']\n", "    axolotl_config_dict['num_epochs'] = training_config_dict['training_arguments']['num_train_epochs']\n", "    axolotl_config_dict['warmup_ratio'] = training_config_dict['training_arguments']['warmup_ratio']\n", "    axolotl_config_dict['learning_rate'] = training_config_dict['training_arguments']['learning_rate']\n", "    axolotl_config_dict['weight_decay'] = training_config_dict['training_arguments']['weight_decay']\n", "    axolotl_config_dict['max_grad_norm'] = training_config_dict['training_arguments']['max_grad_norm']\n", "    axolotl_config_dict['eval_steps'] = training_config_dict['training_arguments']['eval_steps']\n", "    axolotl_config_dict['save_steps'] = training_config_dict['training_arguments']['save_steps']\n", "    axolotl_config_dict['logging_steps'] = training_config_dict['training_arguments']['logging_steps']\n", "    axolotl_config_dict['save_total_limit'] = training_config_dict['training_arguments']['save_total_limit']\n", "    axolotl_config_dict['lr_scheduler'] = training_config_dict['training_arguments']['lr_scheduler_type']\n", "    axolotl_config_dict['optimizer'] = training_config_dict['training_arguments']['optim']\n", "    axolotl_config_dict['use_tensorboard'] = 'true' if training_config_dict['training_arguments']['report_to']=='tensorboard' else 'false'\n", "    axolotl_config_dict['eval_strategy'] = 'no' if training_config_dict['dataset_arguments']['test_split']==0 else ''\n", "    axolotl_config_dict['deepspeed'] = join(os.getcwd(), 'deepspeed_configs/zero2.json') if len(VLLM_CUDA_VISIBLE_DEVICES)//2>1 else ''\n", "\n", "    dataset_prepared_path = join(args.cache_dir, 'axolotl_prepared_dataset')\n", "    if isdir(dataset_prepared_path):\n", "        os.system(f'rm -rf \"{dataset_prepared_path}\"')\n", "    axolotl_config_dict['dataset_prepared_path'] = dataset_prepared_path\n", "\n", "    if args.adapter_type=='LoRA':\n", "        target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                        \"gate_proj\", \"up_proj\", \"down_proj\",]\n", "        axolotl_config_dict['adapter'] = 'lora'\n", "        axolotl_config_dict['lora_target_modules'] = '\\n  '.join([f'- {f}' for f in target_modules])\n", "    if args.adapter_type=='QLoRA':\n", "        axolotl_config_dict['adapter'] = 'qlora'\n", "        axolotl_config_dict['lora_target_linear'] = 'true'\n", "    if args.adapter_type=='LoftQ':\n", "        axolotl_config_dict['adapter'] = 'lora'\n", "        axolotl_config_dict['lora_target_linear'] = 'true'\n", "        axolotl_config_dict['loftq_bits'] = \"peft:\\n  loftq_config:\\n    loftq_bits: {}\".format(args.quant_bits)\n", "    axolotl_config_dict['lora_r'] = args.lora_rank\n", "    axolotl_config_dict['lora_alpha'] = args.lora_alpha if args.lora_alpha else args.lora_rank\n", "    axolotl_config_dict['lora_dropout'] = args.lora_dropout\n", "    axolotl_config_dict['lora_model_dir'] = lora_model_dir if lora_model_dir else ''\n", "\n", "    for k in bnb_config_dict.keys():\n", "        axolotl_config_dict[k] = 'true' if bnb_config_dict[k] else 'false'\n", "\n", "    axolotl_config_yml = yaml.safe_load(axolotl_config.format(**axolotl_config_dict))\n", "    axolotl_config_yml['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['datasets'][0]['type'] = 'reward_functions_cache.grpo_transform'\n", "    axolotl_config_yml['datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['test_datasets'][0]['type'] = 'reward_functions_cache.grpo_transform'\n", "    axolotl_config_yml['test_datasets'][0]['chat_template_jinja'] = dataset_obj.tokenizer.chat_template\n", "    axolotl_config_yml['special_tokens'] = {'pad_token': dataset_obj.tokenizer.pad_token if dataset_obj.tokenizer.pad_token else dataset_obj.tokenizer.eos_token}\n", "    if axolotl_config_yml['deepspeed'] is None or axolotl_config_yml['deepspeed']=='':\n", "        del axolotl_config_yml['deepspeed']\n", "    axolotl_config_yml = yaml.dump(axolotl_config_yml, default_flow_style=False, sort_keys=False)\n", "    axolotl_config_yml = axolotl_config_yml.replace('null', '')\n", "\n", "    axolotl_config_path = join(training_config_dict['training_arguments']['output_dir'], f'{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.yml')\n", "    if not isdir(training_config_dict['training_arguments']['output_dir']):\n", "        os.mkdir(training_config_dict['training_arguments']['output_dir'])\n", "    with open(axolotl_config_path, 'w') as fp:\n", "        fp.write(axolotl_config_yml)\n", "    return axolotl_config_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device = \"cuda:0\"\n", "token = training_config_dict['hub_config']['hf_token'] if training_config_dict['hub_config']['hf_token'] else os.environ['HF_TOKEN']\n", "torch_device = torch.device(device)\n", "huggingface_hub.login(token=token, add_to_git_credential=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token = os.environ['HF_TOKEN']\n", "huggingface_hub.login(token=token, add_to_git_credential=False)\n", "if args.quant_bits==4:\n", "    bnb_config_dict = {\n", "        'load_in_8bit': False,\n", "        'load_in_4bit': True,\n", "        'bnb_4bit_use_double_quant': True,\n", "        'llm_int8_has_fp16_weight': False,\n", "    }\n", "else:\n", "    bnb_config_dict = {\n", "        'load_in_8bit': True,\n", "        'load_in_4bit': False,\n", "        'bnb_4bit_use_double_quant': False,\n", "        'llm_int8_has_fp16_weight': False,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trust_remote_code = training_config_dict['hub_config']['trust_remote_code']\n", "dataset_args = training_config_dict['dataset_arguments']\n", "if not isdir(join(args.cache_dir, args.model_name)):\n", "    model_path = join(args.cache_dir, args.model_name.replace('/', '--'))\n", "    if not isdir(model_path):\n", "        huggingface_hub.snapshot_download(repo_id=args.model_name, local_dir=model_path)\n", "        model_config = AutoConfig.from_pretrained(\n", "            args.model_name,\n", "            token = token,\n", "            trust_remote_code = trust_remote_code,\n", "        )\n", "        pprinter.pprint(model_config)\n", "else:\n", "    model_path = join(args.cache_dir, args.model_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.sft:\n", "    dataset_dict = {}\n", "    if dataset_args['dataset_split'] is not None:\n", "        dataset_dict['split_name'] = dataset_args['dataset_split']\n", "    if dataset_args['dataset_subset'] is not None:\n", "        dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                cache_dir=dataset_args['cache_dir'], \n", "                                test_size=dataset_args['test_split'], \n", "                                **dataset_dict,\n", "                                )\n", "    tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(model_path), chat_template=dataset_args['chat_template'])\n", "    print('Dataset Loaded.')\n", "    print('Performing SFT:')\n", "    print(dataset_obj.iterable_dataset_traineval['train'])\n", "    \n", "    dataset_path = join(dataset_args['cache_dir'], 'processed_axolotl_data')\n", "    if isdir(dataset_path):\n", "        os.system('rm -r \"{}\"'.format(dataset_path))\n", "    dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)\n", "else:\n", "    print('SFT is not selected.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.sft:\n", "    axolotl_config_path = format_config_sft(\n", "        axolotl_config, \n", "        args,\n", "        model_path, \n", "        dataset_path, \n", "        training_config_dict,\n", "        bnb_config_dict,\n", "        dataset_obj,\n", "        None,\n", "        trust_remote_code\n", "    )\n", "    gpu_count = ','.join([str(f) for f in range(accelerator_config_dict['num_processes'])])\n", "    os.environ['TOKENIZERS_PARALLELISM'] = 'false'\n", "    if in_notebook():\n", "        ! echo \"CUDA_VISIBLE_DEVICES: {gpu_count}\"\n", "        ! echo \"Config saved at: {axolotl_config_path}\"\n", "        ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.preprocess \"{axolotl_config_path}\" \n", "        ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.train \"{axolotl_config_path}\" \n", "    else:\n", "        asyncio.run(run_astream_command(f'echo \"CUDA_VISIBLE_DEVICES: {gpu_count}\"'))\n", "        asyncio.run(run_astream_command(f'echo \"Config saved at: {axolotl_config_path}\"'))\n", "        asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.preprocess \"{axolotl_config_path}\"'))\n", "        asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.train \"{axolotl_config_path}\"'))\n", "    \n", "    if args.dpo or args.rl:\n", "        lora_model_dir = training_config_dict['training_arguments']['output_dir']\n", "        if in_notebook():\n", "            os.environ['AXOLOTL_CONFIG_PATH'] = axolotl_config_path\n", "            os.environ['LORA_MODEL_DIR'] = lora_model_dir\n", "            ! python3 -m axolotl.cli.merge_lora \"$AXOLOTL_CONFIG_PATH\" --lora_model_dir=\"$LORA_MODEL_DIR\"\n", "        else:\n", "            asyncio.run(run_astream_command(f'python3 -m axolotl.cli.merge_lora \"{axolotl_config_path}\" --lora_model_dir=\"{lora_model_dir}\"'))\n", "        os.system('mv \"{}\" \"{}\"'.format(training_config_dict['training_arguments']['output_dir'], training_config_dict['training_arguments']['output_dir']+'_sft'))\n", "        model_path = join(training_config_dict['training_arguments']['output_dir']+'_sft', 'merged')\n", "else:\n", "    lora_model_dir = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def formatting_func(examples, return_type: Literal[\"standard\", \"conversational\"] = \"conversational\"):\n", "    prompt = []\n", "    chosen = []\n", "    rejected = []\n", "    conversation_keys = [\"prompt\", \"chosen\", \"rejected\"]\n", "    b_sz = len(examples[\"prompt\"])\n", "    \n", "    for b_id in range(b_sz):\n", "        for convo_key in conversation_keys:\n", "            convo = examples[convo_key][b_id]\n", "            \n", "            if return_type==\"converstional\":\n", "                if convo_key==\"prompt\":\n", "                    prompt.append([{'role': 'user', 'content': convo}])\n", "                elif convo_key == \"chosen\":\n", "                    chosen.append({'role': 'assistant', 'content': convo})\n", "                elif convo_key == \"rejected\":\n", "                    rejected.append({'role': 'assistant', 'content': convo})\n", "            if return_type==\"standard\":\n", "                if convo_key==\"prompt\":\n", "                    prompt.append(convo)\n", "                elif convo_key == \"chosen\":\n", "                    chosen.append(convo)\n", "                elif convo_key == \"rejected\":\n", "                    rejected.append(convo)\n", "    return {\"prompt\": prompt, \"chosen\" : chosen, \"rejected\": rejected}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.dpo:\n", "    dataset_dict = {}\n", "    if dataset_args['dataset_split'] is not None:\n", "        dataset_dict['split_name'] = dataset_args['dataset_split']\n", "    if dataset_args['dataset_subset'] is not None:\n", "        dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                cache_dir=dataset_args['cache_dir'], \n", "                                test_size=dataset_args['test_split'], \n", "                                **dataset_dict,\n", "                                )\n", "    tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(model_path), chat_template=dataset_args['chat_template'], \n", "                                            ft_type='dpo',\n", "                                            chat_headers={\n", "                                                \"system_header\":dataset_args['system_header'], \n", "                                                \"user_header\":dataset_args['user_header'], \n", "                                                \"assistant_header\":dataset_args['assistant_header']\n", "                                            })\n", "    dataset_obj.iterable_dataset_traineval = dataset_obj.iterable_dataset_traineval.map(formatting_func, batched = True,\n", "                                                                                        fn_kwargs = {\n", "                                                                                            'return_type': \"conversational\"\n", "                                                                                        })\n", "    # ndataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "    #                             cache_dir=dataset_args['cache_dir'], \n", "    #                             test_size=dataset_args['test_split'], \n", "    #                             **dataset_dict,\n", "    #                             )\n", "    # dataset_obj.iterable_dataset_traineval['test'] = ndataset_obj.iterable_dataset_traineval['test'].map(lambda x: \\\n", "    #     {\"text\": dataset_obj.tokenizer.apply_chat_template(x['conversations'], tokenize = False)}, batched=False)\n", "    # dataset_obj.iterable_dataset_traineval['test'] = dataset_obj.iterable_dataset_traineval['test'].remove_columns([\n", "    #     f for f in dataset_obj.iterable_dataset_traineval['test'].column_names if f not in [\"text\", \"conversations\"]])\n", "    print('Dataset Loaded.')\n", "    print('Performing DPO:')\n", "    print(dataset_obj.iterable_dataset_traineval)\n", "    \n", "    dataset_path = join(dataset_args['cache_dir'], 'processed_axolotl_data')\n", "    if isdir(dataset_path):\n", "        os.system('rm -r \"{}\"'.format(dataset_path))\n", "    dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)\n", "else:\n", "    print('DPO is not selected.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.dpo:\n", "    axolotl_config_path = format_config_dpo(\n", "        axolotl_config, \n", "        args,\n", "        model_path, \n", "        dataset_path, \n", "        training_config_dict,\n", "        bnb_config_dict,\n", "        dataset_obj,\n", "        lora_model_dir,\n", "        trust_remote_code\n", "    )\n", "    gpu_count = ','.join([str(f) for f in range(accelerator_config_dict['num_processes'])])\n", "    os.environ['TOKENIZERS_PARALLELISM'] = 'false'\n", "    if in_notebook():\n", "        ! echo \"CUDA_VISIBLE_DEVICES: {gpu_count}\"\n", "        ! echo \"Config saved at: {axolotl_config_path}\"\n", "        ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.preprocess \"{axolotl_config_path}\" \n", "        ! CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.train \"{axolotl_config_path}\" \n", "    else:\n", "        asyncio.run(run_astream_command(f'echo \"CUDA_VISIBLE_DEVICES: {gpu_count}\"'))\n", "        asyncio.run(run_astream_command(f'echo \"Config saved at: {axolotl_config_path}\"'))\n", "        asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.preprocess \"{axolotl_config_path}\"'))\n", "        asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES=\"{gpu_count}\" accelerate launch --config_file accelerator_config.yml -m axolotl.cli.train \"{axolotl_config_path}\"'))\n", "    \n", "    if args.sft or args.rl:\n", "        lora_model_dir = training_config_dict['training_arguments']['output_dir']\n", "        if in_notebook():\n", "            os.environ['AXOLOTL_CONFIG_PATH'] = axolotl_config_path\n", "            os.environ['LORA_MODEL_DIR'] = lora_model_dir\n", "            ! python3 -m axolotl.cli.merge_lora \"$AXOLOTL_CONFIG_PATH\" --lora_model_dir=\"$LORA_MODEL_DIR\"\n", "        else:\n", "            asyncio.run(run_astream_command(f'python3 -m axolotl.cli.merge_lora \"{axolotl_config_path}\" --lora_model_dir=\"{lora_model_dir}\"'))\n", "        os.system('mv \"{}\" \"{}\"'.format(training_config_dict['training_arguments']['output_dir'], training_config_dict['training_arguments']['output_dir']+'_dpo'))\n", "        model_path = join(training_config_dict['training_arguments']['output_dir']+'_dpo', 'merged')\n", "else:\n", "    lora_model_dir = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.rl:\n", "    dataset_dict = {}\n", "    if dataset_args['dataset_split'] is not None:\n", "        dataset_dict['split_name'] = dataset_args['dataset_split']\n", "    if dataset_args['dataset_subset'] is not None:\n", "        dataset_dict['subset_name'] = dataset_args['dataset_subset']\n", "    dataset_class = getattr(importlib.import_module(f'dataset_wrappers.{dataset_args[\"dataset_type\"]}'), f'{dataset_args[\"dataset_type\"]}_dataset')\n", "    dataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "                                cache_dir=dataset_args['cache_dir'], \n", "                                test_size=dataset_args['test_split'], \n", "                                **dataset_dict,\n", "                                )\n", "    tokenizer = dataset_obj.format_dataset(tokenizer=AutoTokenizer.from_pretrained(model_path), chat_template=dataset_args['chat_template'], \n", "                                            ft_type='rl',\n", "                                            chat_headers={\n", "                                                \"system_header\":dataset_args['system_header'], \n", "                                                \"user_header\":dataset_args['user_header'], \n", "                                                \"assistant_header\":dataset_args['assistant_header']\n", "                                            })\n", "    \n", "    reward_fn_strings = []\n", "    reward_fn_list = dataset_obj.format_rewards(training_config_dict['trl_arguments']['grpo_reward_funcs'])\n", "    for fn_name in [*reward_fn_list, 'extract_xml_thought', 'extract_xml_answer', 'grpo_transform']:\n", "        reward_fn_strings.append(dataset_obj.reward_model.get_reward_function_as_string(fn_name.__name__ if type(fn_name)!=str else fn_name, verbose=False))\n", "    reward_fn_strings = '\\n\\n'.join(reward_fn_strings)\n", "    with open('./reward_functions_cache.py', 'w') as fp:\n", "        with open('./dataset_wrappers/rewards.py', 'r') as f:\n", "            fp.write(f.read().split('class reward_class:')[0]+'\\n\\n')\n", "        fp.write(reward_fn_strings)\n", "    training_config_dict['trl_arguments']['grpo_reward_funcs'] = [f'reward_functions_cache.{f.__name__}' for f in reward_fn_list]\n", "    \n", "    # ndataset_obj =  dataset_class(dataset_name=dataset_args['dataset_name'], \n", "    #                             cache_dir=dataset_args['cache_dir'], \n", "    #                             test_size=dataset_args['test_split'], \n", "    #                             **dataset_dict,\n", "    #                             )\n", "    # dataset_obj.iterable_dataset_traineval['test'] = ndataset_obj.iterable_dataset_traineval['test'].map(lambda x: \\\n", "    #     {\"text\": dataset_obj.tokenizer.apply_chat_template(x['conversations'], tokenize = False)}, batched=False)\n", "    # dataset_obj.iterable_dataset_traineval['test'] = dataset_obj.iterable_dataset_traineval['test'].remove_columns([\n", "    #     f for f in dataset_obj.iterable_dataset_traineval['test'].column_names if f not in [\"text\", \"conversations\"]])\n", "    print('Dataset Loaded.')\n", "    print('Performing RL:')\n", "    print('Rewards:', [f.__name__  for f in reward_fn_list])\n", "    print(dataset_obj.iterable_dataset_traineval['train'])\n", "    \n", "    dataset_path = join(dataset_args['cache_dir'], 'processed_axolotl_data')\n", "    if isdir(dataset_path):\n", "        os.system('rm -r \"{}\"'.format(dataset_path))\n", "    dataset_obj.iterable_dataset_traineval.save_to_disk(dataset_path)\n", "else:\n", "    print('RL is not selected.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if args.rl:\n", "    axolotl_config_path = format_config_rl(\n", "        axolotl_config, \n", "        args,\n", "        trl_args, \n", "        model_path, \n", "        dataset_path, \n", "        training_config_dict,\n", "        bnb_config_dict,\n", "        dataset_obj,\n", "        lora_model_dir,\n", "        trust_remote_code\n", "    )\n", "    with open(axolotl_config_path, 'r') as fp:\n", "        print(fp.read())\n", "    \n", "    if len(VLLM_CUDA_VISIBLE_DEVICES)//2>0:\n", "        gpu_count = ','.join([str(f) for f in VLLM_CUDA_VISIBLE_DEVICES[:min(len(VLLM_CUDA_VISIBLE_DEVICES), len(VLLM_CUDA_VISIBLE_DEVICES)//2)]])\n", "        vllm_count = ','.join([str(f) for f in VLLM_CUDA_VISIBLE_DEVICES[min(len(VLLM_CUDA_VISIBLE_DEVICES)-1, len(VLLM_CUDA_VISIBLE_DEVICES)//2):]])\n", "    else:\n", "        vllm_count = 0\n", "        gpu_count = ','.join([str(f) for f in VLLM_CUDA_VISIBLE_DEVICES])\n", "    os.environ['PYTHONPATH'] = os.getcwd()\n", "    os.environ['TOKENIZERS_PARALLELISM'] = 'false'\n", "    os.environ['VLLM_VISIBLE_DEVICES'] = vllm_count\n", "    os.environ['CUDA_VISIBLE_DEVICES'] = gpu_count\n", "    os.system('cp \"{}\" \"{}\"'.format('./reward_functions_cache.py', join(os.path.dirname(axolotl_config_path), 'reward_functions_cache.py')))\n", "    num_processes = len(gpu_count.split(','))\n", "    axolotl_ref_model = training_config_dict['hub_config']['model_name']\n", "    if in_notebook():\n", "        ! echo \"VLLM_VISIBLE_DEVICES: $VLLM_VISIBLE_DEVICES\"\n", "        ! echo \"CUDA_VISIBLE_DEVICES: $CUDA_VISIBLE_DEVICES\"\n", "        ! echo \"Config saved at: {axolotl_config_path}\"\n", "        if vllm_count:\n", "            cmd = f'CUDA_VISIBLE_DEVICES={vllm_count} axolotl vllm-serve \"{axolotl_config_path}\"'\n", "            # ! CUDA_VISIBLE_DEVICES=$VLLM_VISIBLE_DEVICES axolotl vllm-serve \"{axolotl_config_path}\" &\n", "            input(f\"Enter the following command on another terminal and wait for VLLM to complete initialization: \\n{cmd} \\nPress ENTER to continue.\")\n", "        ! CUDA_VISIBLE_DEVICES=$CUDA_VISIBLE_DEVICES axolotl preprocess \"{axolotl_config_path}\"\n", "        ! CUDA_VISIBLE_DEVICES=$CUDA_VISIBLE_DEVICES accelerate launch --config_file accelerator_config.yml --num_processes=\"{num_processes}\" -m axolotl.cli.train \"{axolotl_config_path}\"\n", "    else:\n", "        asyncio.run(run_astream_command(f'echo \"VLLM_VISIBLE_DEVICES: $VLLM_VISIBLE_DEVICES\"'))\n", "        asyncio.run(run_astream_command(f'echo \"CUDA_VISIBLE_DEVICES: {gpu_count}\"'))\n", "        asyncio.run(run_astream_command(f'echo \"Config saved at: {axolotl_config_path}\"'))\n", "        if vllm_count:\n", "            cmd = f'CUDA_VISIBLE_DEVICES={vllm_count} axolotl vllm-serve \"{axolotl_config_path}\"'\n", "            # os.system(f'CUDA_VISIBLE_DEVICES={vllm_count} axolotl vllm-serve \"{axolotl_config_path}\" &')\n", "            input(f\"Enter the following command on another terminal and wait for VLLM to complete initialization: \\n{cmd} \\nPress ENTER to continue.\")\n", "        asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES={gpu_count} axolotl preprocess \"{axolotl_config_path}\"'))\n", "        asyncio.run(run_astream_command(f'CUDA_VISIBLE_DEVICES={gpu_count} accelerate launch --config_file accelerator_config.yml --num_processes={num_processes} -m axolotl.cli.train \"{axolotl_config_path}\"'))\n", "        \n", "    if args.save_merged:\n", "        lora_model_dir = training_config_dict['training_arguments']['output_dir']\n", "        if in_notebook():\n", "            os.environ['AXOLOTL_CONFIG_PATH'] = axolotl_config_path\n", "            os.environ['LORA_MODEL_DIR'] = lora_model_dir\n", "            ! python3 -m axolotl.cli.merge_lora \"$AXOLOTL_CONFIG_PATH\" --lora_model_dir=\"$LORA_MODEL_DIR\"\n", "        else:\n", "            asyncio.run(run_astream_command(f'python3 -m axolotl.cli.merge_lora \"{axolotl_config_path}\" --lora_model_dir=\"{lora_model_dir}\"'))\n", "else:\n", "    lora_model_dir = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sygen_conda", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}