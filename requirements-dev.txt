# Development and Optional Requirements for IDSL LLM Framework
# Install these for additional functionality

# Advanced Training Frameworks
deepspeed>=0.10.0

# Flash Attention (requires compatible GPU)
# flash-attn>=2.0.0

# Unsloth for optimized single-GPU training
# Note: May have version conflicts, install manually
# unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git

# Axolotl for advanced multi-GPU training
# axolotl @ git+https://github.com/axolotl-ai-cloud/axolotl.git

# VLLM for fast inference
# vllm>=0.2.0

# Memory optimization
# xformers>=0.0.20

# GPU acceleration
# triton>=2.0.0

# Additional tokenizers
protobuf>=4.21.0

# Development tools
black>=23.0.0
flake8>=6.0.0
pytest>=7.0.0
pre-commit>=3.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# Monitoring and logging
wandb>=0.15.0
tensorboard>=2.13.0

# Additional data processing
opencv-python>=4.8.0
pillow>=10.0.0

# Web scraping (for custom datasets)
beautifulsoup4>=4.12.0
selenium>=4.15.0

# API development
fastapi>=0.100.0
uvicorn>=0.23.0

# Cloud storage
boto3>=1.28.0
google-cloud-storage>=2.10.0

# Distributed computing
ray>=2.6.0

# Model optimization
onnx>=1.14.0
onnxruntime>=1.15.0

# Evaluation metrics
evaluate>=0.4.0
rouge-score>=0.1.2
bleu>=0.2.0
