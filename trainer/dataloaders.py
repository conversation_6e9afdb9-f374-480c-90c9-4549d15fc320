import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset

class custom_dataset():
    def __init__(self, iter_dataset, tokenizer, input_callback, seq_length=512, batch_size=8,
                 keymap=None, valid_features=None, split='train', verbose=False):
        super(custom_dataset, self).__init__()
        self.split = split
        self.tokenizer = tokenizer
        self.max_length = seq_length
        self.create_input_data = input_callback
        
        assert batch_size>-1
        self.batch_size = batch_size
        self.iter_dataset = iter_dataset[split]
        self.keymap = keymap
        if keymap is not None:
            self.iter_dataset.rename_columns(self.keymap)
        self.valid_features = valid_features if valid_features else list(self.iter_dataset.features.keys())
        self.verbose = verbose
    
    def data_builder(self, dataset_dict, batch_size=1):
        data_list = []
        for b_id in range(batch_size):
            if batch_size>1:
                data_list.append({                
                    k:dataset_dict[k][b_id] for k in self.valid_features
                })
            else:
                data_list.append({                
                    k:dataset_dict[k] for k in self.valid_features
                })
        return data_list
    
    def len(self):
        return len(self.iter_dataset)
    
    def getitem(self, dat):
        if not self.batch_size:
            batch_size = len(dat['prompt'])
        else:
            batch_size = self.batch_size
        dat = self.data_builder(dat, batch_size)
        result = {'tokenized_prompt':[], 'attention_mask':[]}
        
        for b_id in range(batch_size):
            data = dat[b_id]
            text_prompt = self.create_input_data(data)
            tokenized_prompt = self.tokenizer(text_prompt, add_special_tokens=True, padding="max_length", max_length=self.max_length)
            result['tokenized_prompt'].append(tokenized_prompt['input_ids'])
            result['attention_mask'].append(tokenized_prompt['attention_mask'])
        return result
    
    def formatter_fn(self, dat):
        batch_size = 1
        dat = self.data_builder(dat, batch_size)
        result = {'tokenized_chosen':[], 'tokenized_rejected':[]}
        
        for b_id in range(batch_size):
            data = dat[b_id]
            text_chosen, text_rejected = self.create_input_dict(data)
            # prompt = self.tokenizer.apply_chat_template(data_dict, tokenize=False, add_generation_prompt=True)
            tokenized_text_chosen = self.tokenizer(text_chosen, add_special_tokens=True,
                                            return_tensors='pt', max_length=2048, padding="max_length"
                                            )
            tokenized_text_rejected = self.tokenizer(text_rejected, add_special_tokens=True,
                                             return_tensors='pt', max_length=2048, padding="max_length"
                                            )
            result['tokenized_chosen'].append(tokenized_prompt)
            result['tokenized_rejected'].append(tokenized_response)
        return result