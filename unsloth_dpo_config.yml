hub_config:
  username: "rg1995007"
  hf_token:
  trust_remote_code: true
  model_name: "meta-llama/Llama-3.2-3B-Instruct"

dataset_arguments:
  test_split: 0.1
  dataset_type: "ultrafeedback"
  dataset_name: "argilla/ultrafeedback-binarized-preferences-cleaned"
  dataset_subset:
  dataset_split:
  cache_dir: "/data/server_cache"
  chat_template: "llama-3.2"
  system_header: "<|start_header_id|>system<|end_header_id|>\n\n"
  user_header: "<|start_header_id|>user<|end_header_id|>\n\n"
  assistant_header: "<|start_header_id|>assistant<|end_header_id|>\n\n"
  # chat_template: "qwen-2.5"
  # system_header: "<|im_start|>system\n"
  # user_header: "<|im_start|>user\n"
  # assistant_header: "<|im_start|>assistant\n"

training_arguments:
  per_device_train_batch_size: 8
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 4
  eval_steps: 0.25
  warmup_ratio: 0.1
  num_train_epochs: 1
  learning_rate: 0.00005
  logging_steps: 1
  weight_decay: 0.01
  seed: 3407
  save_steps: 100
  save_total_limit: 2
  optim: "adamw_8bit"
  lr_scheduler_type: "linear"
  output_dir: "/data/server_cache/outputs"
  report_to: "tensorboard"
