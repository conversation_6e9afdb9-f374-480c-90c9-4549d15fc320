hub_config:
  username: "rg1995007"
  hf_token:
  trust_remote_code: true
  model_name: "/data/server_cache/meta-llama/Llama-3.2-3B-Instruct_adapter_lora_16bit"

dataset_arguments:
  test_split: 0.1
  dataset_type: "openhermes"
  dataset_name: "teknium/OpenHermes-2.5"
  dataset_subset:
  dataset_split:
  cache_dir: "/data/server_cache"
  chat_template: "llama-3.2"
  field_texts: "text"
  field_messages: "prompt"
  field_chosen: "chosen"
  field_rejected: "rejected"
  test_field_messages: "conversations"
  system_header: "<|start_header_id|>system<|end_header_id|>\n\n"
  user_header: "<|start_header_id|>user<|end_header_id|>\n\n"
  assistant_header: "<|start_header_id|>assistant<|end_header_id|>\n\n"

training_arguments:
  fan_in_fan_out: false
  per_device_train_batch_size: 12
  gradient_accumulation_steps: 1
  eval_steps: 0.25
  warmup_ratio: 0.1
  num_train_epochs: 1
  learning_rate: 0.0001
  max_grad_norm: 1.0
  logging_steps: 1
  weight_decay: 0.01
  seed: 3407
  save_steps: 1
  save_total_limit: 2
  scheduler: "linear"
  optim: "adamw_bnb_8bit"
  output_dir: "/data/server_cache/outputs"
  report_to: "tensorboard"
  
trl_arguments:
  rpo_alpha:
  dpo_beta:
  dpo_use_weighting: 
  dpo_loss: 'sigmoid'
  grpo_beta: 0.04
  grpo_num_generations: 6
  grpo_max_completion_length: 8192
  grpo_reward_funcs: ['correctness_reward_func', 'get_mol_format_reward', 'strict_format_reward_func', 'xmlcount_reward_func']
  grpo_reward_weights: 