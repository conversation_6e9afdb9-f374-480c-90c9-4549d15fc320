# IDSL LLM Framework Requirements
# Requires Python 3.12, 3.11, 3.10, or 3.9
# Python 3.13 is NOT supported (incompatible with Unsloth, vLLM, and other packages)
# Recommended: Python 3.12
# Install with: pip install -r requirements.txt

# Core ML Libraries
torch>=2.7.0,<=2.7.0
transformers>=4.53.0,<4.54.0
datasets>=3.6.0,<4.0.0
accelerate>=1.8.0,<2.0.0
peft>=0.15.0,<1.0.0
trl>=0.19.0,<1.0.0

# Quantization and Optimization
bitsandbytes>=0.42.0,<1.0.0
safetensors>=0.5.0,<1.0.0

# Data Processing and Utilities
pyyaml>=6.0.2
numpy>=2.2.0,<3.0.0
pandas>=2.3.0,<3.0.0
tqdm>=4.67.0
regex>=2024.11.0
tokenizers>=0.21.0,<1.0.0
sentencepiece>=0.2.0

# Jupyter and Notebook Support
jupyter>=1.1.0
notebook>=7.4.0
ipywidgets>=8.1.0
matplotlib>=3.10.0
ipykernel>=6.29.0

# Additional ML and Scientific Computing
scipy>=1.15.0
scikit-learn>=1.3.0

# Development and Automation Tools
papermill>=2.4.0

# HTTP and Async Support
aiohttp>=3.12.0
requests>=2.32.0
fsspec>=2025.3.0

# HuggingFace Hub Integration
huggingface-hub>=0.33.0

# File and Data Handling
pyarrow>=20.0.0
dill>=0.3.8
xxhash>=3.5.0

# System and Process Management
psutil>=7.0.0
multiprocess>=0.70.16

# Additional utilities
filelock>=3.18.0
packaging>=25.0

# ============================================================================
# OPTIONAL PACKAGES (Install separately as needed)
# ============================================================================
#
# For Unsloth (optimized single-GPU training):
# pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
#
# For Axolotl (advanced multi-GPU training):
# pip install axolotl
#
# For Flash Attention (requires compatible GPU):
# pip install flash-attn>=2.0.0 --no-build-isolation
#
# For VLLM (fast inference):
# pip install vllm
#
# For additional optimization:
# pip install triton xformers
#
# For DeepSpeed (multi-GPU training):
# pip install deepspeed>=0.10.0
#
# Note: These packages may have specific system requirements or version conflicts.
# Install them individually after the main requirements are working.
