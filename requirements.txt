# IDSL LLM Framework Requirements
# Tested with Python 3.10

# Core ML Libraries
torch>=2.7.0
transformers>=4.53.0
datasets>=3.6.0
accelerate>=1.8.0
peft>=0.15.0
trl>=0.19.0

# Quantization and Optimization
bitsandbytes>=0.42.0
safetensors>=0.5.0

# Data Processing and Utilities
pyyaml>=6.0.2
numpy>=2.2.0
pandas>=2.3.0
tqdm>=4.67.0
regex>=2024.11.0
tokenizers>=0.21.0
sentencepiece>=0.2.0

# Jupyter and Notebook Support
jupyter>=1.1.0
notebook>=7.4.0
ipywidgets>=8.1.0
matplotlib>=3.10.0
ipykernel>=6.29.0

# Additional ML and Scientific Computing
scipy>=1.15.0
scikit-learn>=1.3.0

# Development and Automation Tools
papermill>=2.4.0

# HTTP and Async Support
aiohttp>=3.12.0
requests>=2.32.0
fsspec>=2025.3.0

# HuggingFace Hub Integration
huggingface-hub>=0.33.0

# File and Data Handling
pyarrow>=20.0.0
dill>=0.3.8
xxhash>=3.5.0

# System and Process Management
psutil>=7.0.0
multiprocess>=0.70.16

# Optional: DeepSpeed (for multi-GPU training)
# deepspeed>=0.10.0

# Optional: Unsloth (for optimized single-GPU training)
# Note: May have version conflicts, install manually if needed
unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git

# Optional: Axolotl (for advanced multi-GPU training)
axolotl

# Optional: Flash Attention (if supported on your system)
flash-attn>=2.0.0

# Optional: VLLM (for inference)
vllm

# Optional: Additional optimization libraries
triton
xformers
