# Core ML Libraries
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
accelerate>=0.24.0
peft>=0.6.0
trl>=0.7.0

# Quantization and Optimization
bitsandbytes>=0.41.0

# Data Processing
pyyaml>=6.0
numpy>=1.24.0
pandas>=2.0.0
tqdm>=4.65.0

# Jupyter and Notebook Support
jupyter>=1.0.0
notebook>=6.5.0
ipywidgets>=8.0.0
matplotlib>=3.7.0

# Additional ML Tools
scikit-learn>=1.3.0
scipy>=1.11.0

# Development Tools
papermill>=2.4.0

# DeepSpeed (for multi-GPU training)
deepspeed>=0.10.0

# Optional: Axolotl dependencies
# axolotl

# Optional: Flash Attention (if supported on your system)
# flash-attn>=2.0.0
